"""Unit tests for validation utilities."""

import unittest
from pathlib import Path
import tempfile
import os

from utils.validators import URLValidator, FileValidator


class TestURLValidator(unittest.TestCase):
    """Test URL validation functionality."""
    
    def test_valid_urls(self):
        """Test valid URL validation."""
        valid_urls = [
            "https://www.example.com/file.pdf",
            "http://example.com/image.jpg",
            "https://cdn.example.com/video.mp4",
            "ftp://files.example.com/archive.zip"
        ]
        
        for url in valid_urls:
            with self.subTest(url=url):
                self.assertTrue(URLValidator.is_valid_url(url))
    
    def test_invalid_urls(self):
        """Test invalid URL validation."""
        invalid_urls = [
            "not_a_url",
            "http://",
            "https://",
            "ftp://",
            "",
            "javascript:alert('test')",
            "file:///local/file.txt"
        ]
        
        for url in invalid_urls:
            with self.subTest(url=url):
                self.assertFalse(URLValidator.is_valid_url(url))
    
    def test_filename_extraction(self):
        """Test filename extraction from URLs."""
        test_cases = [
            ("https://example.com/file.pdf", "file.pdf"),
            ("http://example.com/path/to/image.jpg", "image.jpg"),
            ("https://example.com/download?file=document.docx", "document.docx"),  # Should extract from query
            ("https://example.com/", None),
            ("https://example.com/file%20with%20spaces.txt", "file with spaces.txt")
        ]
        
        for url, expected in test_cases:
            with self.subTest(url=url):
                result = URLValidator.extract_filename_from_url(url)
                self.assertEqual(result, expected)
    
    def test_file_extension_extraction(self):
        """Test file extension extraction."""
        test_cases = [
            ("https://example.com/file.pdf", ".pdf"),
            ("http://example.com/image.JPG", ".jpg"),
            ("https://example.com/video.mp4", ".mp4"),
            ("https://example.com/noextension", None),
            ("https://example.com/", None)
        ]
        
        for url, expected in test_cases:
            with self.subTest(url=url):
                result = URLValidator.get_file_extension_from_url(url)
                self.assertEqual(result, expected)
    
    def test_supported_file_types(self):
        """Test supported file type checking."""
        supported_urls = [
            "https://example.com/file.pdf",
            "https://example.com/image.jpg",
            "https://example.com/video.mp4",
            "https://example.com/archive.zip"
        ]
        
        for url in supported_urls:
            with self.subTest(url=url):
                self.assertTrue(URLValidator.is_supported_file_type(url))
    
    def test_filename_sanitization(self):
        """Test filename sanitization."""
        test_cases = [
            ("file<name>.txt", "file_name_.txt"),
            ("file:name.pdf", "file_name.pdf"),
            ("file|name.jpg", "file_name.jpg"),
            ("  file name  ", "file name"),
            ("", "download"),
            ("a" * 300 + ".txt", "a" * 251 + ".txt")  # Test length limit
        ]
        
        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = URLValidator.sanitize_filename(input_name)
                self.assertEqual(result, expected)
    
    def test_unique_filename_generation(self):
        """Test unique filename generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create a file
            test_file = temp_path / "test.txt"
            test_file.write_text("test content")
            
            # Test unique filename generation
            unique_name = URLValidator.generate_unique_filename(temp_path, "test.txt")
            self.assertEqual(unique_name, "test_1.txt")
            
            # Create the first duplicate
            (temp_path / "test_1.txt").write_text("test content")
            
            # Test second unique filename
            unique_name2 = URLValidator.generate_unique_filename(temp_path, "test.txt")
            self.assertEqual(unique_name2, "test_2.txt")


class TestFileValidator(unittest.TestCase):
    """Test file validation functionality."""
    
    def test_valid_download_path(self):
        """Test download path validation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test existing directory
            self.assertTrue(FileValidator.is_valid_download_path(temp_dir))
            
            # Test non-existing but creatable path
            new_path = os.path.join(temp_dir, "new_dir", "file.txt")
            self.assertTrue(FileValidator.is_valid_download_path(new_path))
    
    def test_file_size_calculation(self):
        """Test file size calculation."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            test_content = b"Hello, World!" * 100
            temp_file.write(test_content)
            temp_file.flush()
            temp_file.close()  # Close file before accessing

            file_path = Path(temp_file.name)
            size = FileValidator.get_file_size(file_path)
            self.assertEqual(size, len(test_content))

            # Cleanup
            try:
                file_path.unlink()
            except PermissionError:
                pass  # Ignore cleanup errors on Windows
    
    def test_checksum_calculation(self):
        """Test checksum calculation."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            test_content = b"Hello, World!"
            temp_file.write(test_content)
            temp_file.flush()
            temp_file.close()  # Close file before accessing

            file_path = Path(temp_file.name)

            # Test MD5 checksum
            md5_checksum = FileValidator.calculate_checksum(file_path, 'md5')
            self.assertIsNotNone(md5_checksum)
            self.assertEqual(len(md5_checksum), 32)  # MD5 is 32 hex characters

            # Test SHA256 checksum
            sha256_checksum = FileValidator.calculate_checksum(file_path, 'sha256')
            self.assertIsNotNone(sha256_checksum)
            self.assertEqual(len(sha256_checksum), 64)  # SHA256 is 64 hex characters

            # Cleanup
            try:
                file_path.unlink()
            except PermissionError:
                pass  # Ignore cleanup errors on Windows
    
    def test_file_integrity_verification(self):
        """Test file integrity verification."""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            test_content = b"Hello, World!"
            temp_file.write(test_content)
            temp_file.flush()
            temp_file.close()  # Close file before accessing

            file_path = Path(temp_file.name)

            # Test size verification
            self.assertTrue(FileValidator.verify_file_integrity(
                file_path, expected_size=len(test_content)
            ))

            self.assertFalse(FileValidator.verify_file_integrity(
                file_path, expected_size=len(test_content) + 1
            ))

            # Test checksum verification
            correct_checksum = FileValidator.calculate_checksum(file_path, 'md5')
            self.assertTrue(FileValidator.verify_file_integrity(
                file_path, expected_checksum=correct_checksum
            ))

            self.assertFalse(FileValidator.verify_file_integrity(
                file_path, expected_checksum="wrong_checksum"
            ))

            # Cleanup
            try:
                file_path.unlink()
            except PermissionError:
                pass  # Ignore cleanup errors on Windows


if __name__ == '__main__':
    unittest.main()
