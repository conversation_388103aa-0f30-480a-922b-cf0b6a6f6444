"""Logging utilities for the Tool Image Downloader application."""

import logging
import os
from pathlib import Path
from datetime import datetime
from typing import Optional


class DownloadLogger:
    """Custom logger for download operations."""
    
    def __init__(self, log_level: str = "INFO", log_file: Optional[str] = None):
        self.logger = logging.getLogger("ToolImageDownloader")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Set default log file if not provided
        if log_file is None:
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file = log_dir / f"downloader_{timestamp}.log"
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_formatter = logging.Formatter(
            '%(levelname)s: %(message)s'
        )
        
        # File handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_level.upper()))
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str) -> None:
        """Log debug message."""
        self.logger.debug(message)
    
    def info(self, message: str) -> None:
        """Log info message."""
        self.logger.info(message)
    
    def warning(self, message: str) -> None:
        """Log warning message."""
        self.logger.warning(message)
    
    def error(self, message: str) -> None:
        """Log error message."""
        self.logger.error(message)
    
    def critical(self, message: str) -> None:
        """Log critical message."""
        self.logger.critical(message)
    
    def download_started(self, url: str, filename: str) -> None:
        """Log download start."""
        self.info(f"Download started: {filename} from {url}")
    
    def download_completed(self, filename: str, size: int, duration: float) -> None:
        """Log download completion."""
        size_mb = size / (1024 * 1024)
        speed = size_mb / duration if duration > 0 else 0
        self.info(f"Download completed: {filename} ({size_mb:.2f} MB in {duration:.2f}s, {speed:.2f} MB/s)")
    
    def download_failed(self, url: str, filename: str, error: str) -> None:
        """Log download failure."""
        self.error(f"Download failed: {filename} from {url} - {error}")
    
    def download_paused(self, filename: str) -> None:
        """Log download pause."""
        self.info(f"Download paused: {filename}")
    
    def download_resumed(self, filename: str) -> None:
        """Log download resume."""
        self.info(f"Download resumed: {filename}")


# Global logger instance
logger = DownloadLogger()
