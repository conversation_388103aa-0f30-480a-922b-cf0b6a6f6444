[{"timestamp": "2025-06-01T18:23:34.496201", "url": "https://via.placeholder.com/150x150.png", "filename": "150x150.png", "status": "failed", "file_size": 0, "download_time": 0, "error_message": "HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /150x150.png (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x00000228A9261450>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)\"))"}, {"timestamp": "2025-06-01T18:23:47.028231", "url": "https://via.placeholder.com/200x200.png", "filename": "200x200.png", "status": "failed", "file_size": 0, "download_time": 0, "error_message": "HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /200x200.png (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x00000228A9284550>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)\"))"}, {"timestamp": "2025-06-01T18:23:47.032278", "url": "https://via.placeholder.com/300x200.jpg", "filename": "300x200.jpg", "status": "failed", "file_size": 0, "download_time": 0, "error_message": "HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /300x200.jpg (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x00000228A92847D0>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)\"))"}, {"timestamp": "2025-06-01T18:23:47.033086", "url": "https://via.placeholder.com/250x250.gif", "filename": "250x250.gif", "status": "failed", "file_size": 0, "download_time": 0, "error_message": "HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /250x250.gif (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x00000228A9284690>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)\"))"}]