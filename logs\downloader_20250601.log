2025-06-01 17:48:08,271 - FileDownloader - INFO - Starting File Downloader Pro
2025-06-01 17:48:09,982 - FileDownloader - INFO - Download scheduler started
2025-06-01 17:48:09,995 - FileDownloader - INFO - <PERSON><PERSON> initialized successfully
2025-06-01 17:50:04,453 - FileDownloader - INFO - Download scheduler started
2025-06-01 17:50:04,460 - FileDownloader - INFO - Added download: json from https://httpbin.org/json
2025-06-01 17:50:04,460 - FileDownloader - INFO - Download started: json from https://httpbin.org/json
2025-06-01 17:50:06,049 - FileDownloader - INFO - Download completed: json (0.00 MB in 1.59s, 0.00 MB/s)
2025-06-01 17:50:07,469 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 17:50:07,472 - FileDownloader - INFO - Download scheduler started
2025-06-01 17:50:07,473 - FileDownloader - INFO - Added download: json_1 from https://httpbin.org/json
2025-06-01 17:50:07,473 - FileDownloader - INFO - Added download: xml from https://httpbin.org/xml
2025-06-01 17:50:07,473 - FileDownloader - INFO - Added download: html from https://httpbin.org/html
2025-06-01 17:50:07,474 - FileDownloader - INFO - Download started: json_1 from https://httpbin.org/json
2025-06-01 17:50:07,474 - FileDownloader - INFO - Download started: xml from https://httpbin.org/xml
2025-06-01 17:50:07,474 - FileDownloader - INFO - Download started: html from https://httpbin.org/html
2025-06-01 17:50:08,555 - FileDownloader - INFO - Download completed: json_1 (0.00 MB in 1.08s, 0.00 MB/s)
2025-06-01 17:50:08,628 - FileDownloader - INFO - Download completed: xml (0.00 MB in 1.15s, 0.00 MB/s)
2025-06-01 17:50:08,957 - FileDownloader - INFO - Download completed: html (0.00 MB in 1.48s, 0.00 MB/s)
2025-06-01 17:50:09,979 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 17:51:46,380 - FileDownloader - INFO - Starting File Downloader Pro
2025-06-01 17:51:46,840 - FileDownloader - INFO - Download scheduler started
2025-06-01 17:51:46,852 - FileDownloader - INFO - GUI initialized successfully
2025-06-01 17:52:58,161 - FileDownloader - INFO - Added download: sdkfz-261-2 from https://www.worldwarphotos.info/gallery/germany/armored_vehicles/sdkfz_221_222_223/sdkfz-261-2/
2025-06-01 17:52:58,162 - FileDownloader - INFO - Download started: sdkfz-261-2 from https://www.worldwarphotos.info/gallery/germany/armored_vehicles/sdkfz_221_222_223/sdkfz-261-2/
2025-06-01 17:52:59,452 - FileDownloader - INFO - Download completed: sdkfz-261-2 (0.03 MB in 1.29s, 0.02 MB/s)
2025-06-01 17:53:47,056 - FileDownloader - INFO - Added download: sdkfz-261-2_1 from https://www.worldwarphotos.info/gallery/germany/armored_vehicles/sdkfz_221_222_223/sdkfz-261-2/
2025-06-01 17:53:47,056 - FileDownloader - INFO - Download started: sdkfz-261-2_1 from https://www.worldwarphotos.info/gallery/germany/armored_vehicles/sdkfz_221_222_223/sdkfz-261-2/
2025-06-01 17:53:48,010 - FileDownloader - INFO - Download completed: sdkfz-261-2_1 (0.03 MB in 0.95s, 0.03 MB/s)
2025-06-01 17:54:37,097 - FileDownloader - INFO - Added download: sdkfz-261-2 from https://www.worldwarphotos.info/gallery/germany/armored_vehicles/sdkfz_221_222_223/sdkfz-261-2/
2025-06-01 17:54:37,097 - FileDownloader - INFO - Download started: sdkfz-261-2 from https://www.worldwarphotos.info/gallery/germany/armored_vehicles/sdkfz_221_222_223/sdkfz-261-2/
2025-06-01 17:54:37,944 - FileDownloader - INFO - Download completed: sdkfz-261-2 (0.03 MB in 0.85s, 0.03 MB/s)
2025-06-01 17:54:49,905 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 17:54:49,938 - FileDownloader - INFO - Application shutdown
2025-06-01 18:06:59,791 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:06:59,792 - FileDownloader - ERROR - Failed to add download for https://httpbin.org/image/png: Invalid image URL: URL does not appear to point to an image file. Supported formats: .bmp, .gif, .ico, .jpeg, .jpg, .png, .svg, .webp
2025-06-01 18:06:59,793 - FileDownloader - ERROR - Failed to add download for https://httpbin.org/image/jpeg: Invalid image URL: URL does not appear to point to an image file. Supported formats: .bmp, .gif, .ico, .jpeg, .jpg, .png, .svg, .webp
2025-06-01 18:06:59,793 - FileDownloader - ERROR - Failed to add download for https://httpbin.org/image/webp: Invalid image URL: URL does not appear to point to an image file. Supported formats: .bmp, .gif, .ico, .jpeg, .jpg, .png, .svg, .webp
2025-06-01 18:06:59,793 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:07:27,617 - FileDownloader - INFO - Added image download: png from https://httpbin.org/image/png
2025-06-01 18:07:27,618 - FileDownloader - INFO - Download started: png from https://httpbin.org/image/png
2025-06-01 18:07:29,538 - FileDownloader - ERROR - Download failed: png from https://httpbin.org/image/png - Downloaded file is not a valid image
2025-06-01 18:07:29,619 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:07:31,391 - FileDownloader - INFO - Added image download: png from https://httpbin.org/image/png
2025-06-01 18:07:32,542 - FileDownloader - INFO - Added image download: jpeg from https://httpbin.org/image/jpeg
2025-06-01 18:07:34,503 - FileDownloader - INFO - Added image download: webp from https://httpbin.org/image/webp
2025-06-01 18:07:34,503 - FileDownloader - INFO - Download started: png from https://httpbin.org/image/png
2025-06-01 18:07:34,504 - FileDownloader - INFO - Download started: jpeg from https://httpbin.org/image/jpeg
2025-06-01 18:07:34,504 - FileDownloader - INFO - Download started: webp from https://httpbin.org/image/webp
2025-06-01 18:07:35,760 - FileDownloader - ERROR - Download failed: webp from https://httpbin.org/image/webp - Downloaded file is not a valid image
2025-06-01 18:07:36,003 - FileDownloader - ERROR - Download failed: jpeg from https://httpbin.org/image/jpeg - Downloaded file is not a valid image
2025-06-01 18:07:36,205 - FileDownloader - ERROR - Download failed: png from https://httpbin.org/image/png - Downloaded file is not a valid image
2025-06-01 18:07:36,505 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:08:08,164 - FileDownloader - INFO - Added image download: 150x150.png from https://via.placeholder.com/150x150.png
2025-06-01 18:08:08,165 - FileDownloader - INFO - Download started: 150x150.png from https://via.placeholder.com/150x150.png
2025-06-01 18:08:20,329 - FileDownloader - ERROR - Download failed: 150x150.png from https://via.placeholder.com/150x150.png - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /150x150.png (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001EE74301450>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:08:20,671 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:08:20,685 - FileDownloader - INFO - Added image download: 200x200.png from https://via.placeholder.com/200x200.png
2025-06-01 18:08:20,685 - FileDownloader - INFO - Added image download: 300x200.jpg from https://via.placeholder.com/300x200.jpg
2025-06-01 18:08:20,685 - FileDownloader - INFO - Added image download: 250x250.gif from https://via.placeholder.com/250x250.gif
2025-06-01 18:08:20,686 - FileDownloader - INFO - Download started: 200x200.png from https://via.placeholder.com/200x200.png
2025-06-01 18:08:20,686 - FileDownloader - INFO - Download started: 300x200.jpg from https://via.placeholder.com/300x200.jpg
2025-06-01 18:08:20,686 - FileDownloader - INFO - Download started: 250x250.gif from https://via.placeholder.com/250x250.gif
2025-06-01 18:08:32,834 - FileDownloader - ERROR - Download failed: 200x200.png from https://via.placeholder.com/200x200.png - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /200x200.png (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001EE743247D0>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:08:32,834 - FileDownloader - ERROR - Download failed: 250x250.gif from https://via.placeholder.com/250x250.gif - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /250x250.gif (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001EE74324550>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:08:32,834 - FileDownloader - ERROR - Download failed: 300x200.jpg from https://via.placeholder.com/300x200.jpg - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /300x200.jpg (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001EE74324690>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:08:33,193 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:08:41,970 - FileDownloader - INFO - Starting Image Downloader Pro
2025-06-01 18:08:42,448 - FileDownloader - INFO - GUI initialized successfully
2025-06-01 18:10:02,155 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:10:02,175 - FileDownloader - INFO - Application shutdown
2025-06-01 18:10:09,991 - FileDownloader - INFO - Starting Image Downloader Pro
2025-06-01 18:10:10,434 - FileDownloader - INFO - GUI initialized successfully
2025-06-01 18:10:35,511 - FileDownloader - INFO - Download scheduler stopped
2025-06-01 18:10:35,529 - FileDownloader - INFO - Application shutdown
2025-06-01 18:23:04,643 - ToolImageDownloader - INFO - Starting Tool Image Downloader
2025-06-01 18:23:05,103 - ToolImageDownloader - INFO - GUI initialized successfully
2025-06-01 18:23:21,989 - ToolImageDownloader - INFO - Added image download: 150x150.png from https://via.placeholder.com/150x150.png
2025-06-01 18:23:21,990 - ToolImageDownloader - INFO - Download started: 150x150.png from https://via.placeholder.com/150x150.png
2025-06-01 18:23:34,086 - ToolImageDownloader - ERROR - Download failed: 150x150.png from https://via.placeholder.com/150x150.png - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /150x150.png (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000228A9261450>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:23:34,499 - ToolImageDownloader - INFO - Download scheduler stopped
2025-06-01 18:23:34,521 - ToolImageDownloader - INFO - Added image download: 200x200.png from https://via.placeholder.com/200x200.png
2025-06-01 18:23:34,521 - ToolImageDownloader - INFO - Added image download: 300x200.jpg from https://via.placeholder.com/300x200.jpg
2025-06-01 18:23:34,521 - ToolImageDownloader - INFO - Added image download: 250x250.gif from https://via.placeholder.com/250x250.gif
2025-06-01 18:23:34,522 - ToolImageDownloader - INFO - Download started: 200x200.png from https://via.placeholder.com/200x200.png
2025-06-01 18:23:34,522 - ToolImageDownloader - INFO - Download started: 300x200.jpg from https://via.placeholder.com/300x200.jpg
2025-06-01 18:23:34,522 - ToolImageDownloader - INFO - Download started: 250x250.gif from https://via.placeholder.com/250x250.gif
2025-06-01 18:23:46,619 - ToolImageDownloader - ERROR - Download failed: 200x200.png from https://via.placeholder.com/200x200.png - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /200x200.png (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000228A9284550>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:23:46,619 - ToolImageDownloader - ERROR - Download failed: 250x250.gif from https://via.placeholder.com/250x250.gif - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /250x250.gif (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000228A9284690>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:23:46,620 - ToolImageDownloader - ERROR - Download failed: 300x200.jpg from https://via.placeholder.com/300x200.jpg - HTTPSConnectionPool(host='via.placeholder.com', port=443): Max retries exceeded with url: /300x200.jpg (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x00000228A92847D0>: Failed to resolve 'via.placeholder.com' ([Errno 11001] getaddrinfo failed)"))
2025-06-01 18:23:47,033 - ToolImageDownloader - INFO - Download scheduler stopped
2025-06-01 18:25:01,991 - ToolImageDownloader - INFO - Download scheduler stopped
2025-06-01 18:25:02,016 - ToolImageDownloader - INFO - Application shutdown
