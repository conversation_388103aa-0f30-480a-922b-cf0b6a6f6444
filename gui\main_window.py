"""Main GUI window for the image downloader application."""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from pathlib import Path
from typing import Dict, Any, Optional

try:
    from ttkthemes import ThemedTk, ThemedStyle
    THEMES_AVAILABLE = True
except ImportError:
    THEMES_AVAILABLE = False

from core.download_manager import DownloadManager, DownloadStatus
from core.file_handler import ImageFileHandler
from utils.config import config
from utils.logger import logger
from utils.validators import ImageURLValidator


class DownloadProgressFrame(ttk.Frame):
    """Frame for displaying download progress."""
    
    def __init__(self, parent, download_info: Dict[str, Any]):
        super().__init__(parent)
        self.download_info = download_info
        self.download_id = download_info.get('download_id', '')
        
        self.create_widgets()
        self.update_progress(download_info)
    
    def create_widgets(self):
        """Create progress widgets."""
        # Filename label
        self.filename_label = ttk.Label(self, text=self.download_info.get('filename', 'Unknown'))
        self.filename_label.grid(row=0, column=0, columnspan=4, sticky='w', padx=5, pady=2)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self, variable=self.progress_var, maximum=100, length=300
        )
        self.progress_bar.grid(row=1, column=0, columnspan=2, sticky='ew', padx=5, pady=2)
        
        # Status label
        self.status_label = ttk.Label(self, text="Pending")
        self.status_label.grid(row=1, column=2, padx=5, pady=2)
        
        # Control buttons
        self.pause_button = ttk.Button(self, text="Pause", width=8)
        self.pause_button.grid(row=1, column=3, padx=2, pady=2)
        
        # Info labels
        self.info_frame = ttk.Frame(self)
        self.info_frame.grid(row=2, column=0, columnspan=4, sticky='ew', padx=5, pady=2)
        
        self.speed_label = ttk.Label(self.info_frame, text="Speed: 0 KB/s")
        self.speed_label.grid(row=0, column=0, sticky='w')
        
        self.size_label = ttk.Label(self.info_frame, text="Size: 0 MB")
        self.size_label.grid(row=0, column=1, sticky='w', padx=10)
        
        self.eta_label = ttk.Label(self.info_frame, text="ETA: --")
        self.eta_label.grid(row=0, column=2, sticky='w', padx=10)
        
        # Configure grid weights
        self.columnconfigure(0, weight=1)
        self.info_frame.columnconfigure(0, weight=1)
    
    def update_progress(self, download_info: Dict[str, Any]):
        """Update progress display."""
        self.download_info = download_info
        
        # Update progress bar
        progress = download_info.get('progress', 0)
        self.progress_var.set(progress)
        
        # Update status
        status = download_info.get('status', 'pending')
        self.status_label.config(text=status.title())
        
        # Update speed
        speed = download_info.get('speed', 0)
        if speed > 0:
            if speed > 1024 * 1024:  # MB/s
                speed_text = f"Speed: {speed / (1024 * 1024):.1f} MB/s"
            else:  # KB/s
                speed_text = f"Speed: {speed / 1024:.1f} KB/s"
        else:
            speed_text = "Speed: 0 KB/s"
        self.speed_label.config(text=speed_text)
        
        # Update size
        total_bytes = download_info.get('total_bytes', 0)
        downloaded_bytes = download_info.get('downloaded_bytes', 0)
        if total_bytes > 0:
            total_mb = total_bytes / (1024 * 1024)
            downloaded_mb = downloaded_bytes / (1024 * 1024)
            size_text = f"Size: {downloaded_mb:.1f}/{total_mb:.1f} MB"
        else:
            size_text = "Size: Unknown"
        self.size_label.config(text=size_text)
        
        # Update ETA
        eta = download_info.get('eta', 0)
        if eta > 0:
            if eta > 3600:  # Hours
                eta_text = f"ETA: {eta / 3600:.1f}h"
            elif eta > 60:  # Minutes
                eta_text = f"ETA: {eta / 60:.1f}m"
            else:  # Seconds
                eta_text = f"ETA: {eta:.0f}s"
        else:
            eta_text = "ETA: --"
        self.eta_label.config(text=eta_text)
        
        # Update button state
        if status == DownloadStatus.DOWNLOADING:
            self.pause_button.config(text="Pause", state='normal')
        elif status == DownloadStatus.PAUSED:
            self.pause_button.config(text="Resume", state='normal')
        else:
            self.pause_button.config(state='disabled')


class MainWindow:
    """Main application window."""
    
    def __init__(self):
        self.setup_window()
        self.download_manager = DownloadManager()
        self.file_handler = ImageFileHandler()
        self.download_frames = {}
        
        self.create_widgets()
        self.setup_bindings()
        self.start_update_thread()
    
    def setup_window(self):
        """Setup main window."""
        if THEMES_AVAILABLE:
            self.root = ThemedTk(theme=config.get("theme", "arc"))
        else:
            self.root = tk.Tk()
        
        self.root.title("Image Downloader Pro")
        self.root.geometry(config.get("window_geometry", "800x600"))
        self.root.minsize(600, 400)
        
        # Configure style
        if THEMES_AVAILABLE:
            self.style = ThemedStyle(self.root)
        else:
            self.style = ttk.Style()
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(1, weight=1)
    
    def create_widgets(self):
        """Create main widgets."""
        # Top frame for URL input and controls
        self.top_frame = ttk.Frame(self.root)
        self.top_frame.grid(row=0, column=0, sticky='ew', padx=10, pady=10)
        self.top_frame.columnconfigure(1, weight=1)
        
        # URL input
        ttk.Label(self.top_frame, text="URL:").grid(row=0, column=0, sticky='w', padx=(0, 5))
        
        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(self.top_frame, textvariable=self.url_var, font=('Arial', 10))
        self.url_entry.grid(row=0, column=1, sticky='ew', padx=(0, 5))
        
        self.download_button = ttk.Button(
            self.top_frame, text="Download", command=self.start_download
        )
        self.download_button.grid(row=0, column=2, padx=(0, 5))
        
        self.browse_button = ttk.Button(
            self.top_frame, text="Browse", command=self.browse_destination
        )
        self.browse_button.grid(row=0, column=3)
        
        # Destination path
        ttk.Label(self.top_frame, text="Destination:").grid(row=1, column=0, sticky='w', padx=(0, 5), pady=(5, 0))
        
        self.dest_var = tk.StringVar(value=config.get("download_directory", "./downloads"))
        self.dest_entry = ttk.Entry(self.top_frame, textvariable=self.dest_var, font=('Arial', 9))
        self.dest_entry.grid(row=1, column=1, columnspan=3, sticky='ew', pady=(5, 0))
        
        # Batch download frame
        self.batch_frame = ttk.LabelFrame(self.top_frame, text="Batch Download")
        self.batch_frame.grid(row=2, column=0, columnspan=4, sticky='ew', pady=(10, 0))
        self.batch_frame.columnconfigure(1, weight=1)
        
        ttk.Label(self.batch_frame, text="URLs (one per line):").grid(row=0, column=0, sticky='nw', padx=5, pady=5)
        
        self.batch_text = tk.Text(self.batch_frame, height=3, font=('Arial', 9))
        self.batch_text.grid(row=0, column=1, sticky='ew', padx=5, pady=5)
        
        self.batch_scrollbar = ttk.Scrollbar(self.batch_frame, orient='vertical', command=self.batch_text.yview)
        self.batch_scrollbar.grid(row=0, column=2, sticky='ns', pady=5)
        self.batch_text.config(yscrollcommand=self.batch_scrollbar.set)
        
        self.batch_download_button = ttk.Button(
            self.batch_frame, text="Download All", command=self.start_batch_download
        )
        self.batch_download_button.grid(row=0, column=3, padx=5, pady=5)
        
        # Main content area with notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.grid(row=1, column=0, sticky='nsew', padx=10, pady=(0, 10))
        
        # Downloads tab
        self.downloads_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.downloads_frame, text="Downloads")
        
        # Create downloads list with scrollbar
        self.downloads_canvas = tk.Canvas(self.downloads_frame)
        self.downloads_scrollbar = ttk.Scrollbar(
            self.downloads_frame, orient='vertical', command=self.downloads_canvas.yview
        )
        self.downloads_scrollable_frame = ttk.Frame(self.downloads_canvas)
        
        self.downloads_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.downloads_canvas.configure(scrollregion=self.downloads_canvas.bbox("all"))
        )
        
        self.downloads_canvas.create_window((0, 0), window=self.downloads_scrollable_frame, anchor="nw")
        self.downloads_canvas.configure(yscrollcommand=self.downloads_scrollbar.set)
        
        self.downloads_canvas.grid(row=0, column=0, sticky='nsew')
        self.downloads_scrollbar.grid(row=0, column=1, sticky='ns')
        
        self.downloads_frame.columnconfigure(0, weight=1)
        self.downloads_frame.rowconfigure(0, weight=1)
        
        # History tab
        self.history_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.history_frame, text="History")
        
        # Settings tab
        self.settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_frame, text="Settings")
        self.create_settings_tab()

        # History tab content
        self.create_history_tab()
        
        # Status bar
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.grid(row=2, column=0, sticky='ew', padx=10, pady=(0, 5))
        
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.status_label.grid(row=0, column=0, sticky='w')
        
        # Queue status
        self.queue_status_var = tk.StringVar(value="Queue: 0 active, 0 pending")
        self.queue_status_label = ttk.Label(self.status_frame, textvariable=self.queue_status_var)
        self.queue_status_label.grid(row=0, column=1, sticky='e')
        
        self.status_frame.columnconfigure(0, weight=1)
    
    def setup_bindings(self):
        """Setup event bindings."""
        self.url_entry.bind('<Return>', lambda e: self.start_download())
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Drag and drop support (basic)
        self.url_entry.bind('<Button-1>', self.on_url_click)
    
    def on_url_click(self, event):
        """Handle URL entry click."""
        # Basic clipboard paste support for image URLs
        try:
            clipboard_content = self.root.clipboard_get()
            is_valid, _ = ImageURLValidator.validate_image_url(clipboard_content)
            if is_valid:
                self.url_var.set(clipboard_content)
        except:
            pass
    
    def browse_destination(self):
        """Browse for destination directory."""
        directory = filedialog.askdirectory(
            title="Select Download Directory",
            initialdir=self.dest_var.get()
        )
        if directory:
            self.dest_var.set(directory)
    
    def start_download(self):
        """Start a single download."""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a URL")
            return
        
        # Validate image URL
        is_valid, error_message = ImageURLValidator.validate_image_url(url)
        if not is_valid:
            messagebox.showerror("Error", f"Invalid image URL: {error_message}")
            return
        
        destination = self.dest_var.get().strip()
        if not destination:
            destination = config.get("download_directory", "./downloads")
        
        try:
            self.download_manager.add_download(
                url, destination=destination, progress_callback=self.update_download_progress
            )
            self.status_var.set(f"Started image download: {url}")
            self.url_var.set("")  # Clear URL field
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start download: {e}")
    
    def start_batch_download(self):
        """Start batch downloads."""
        urls_text = self.batch_text.get("1.0", tk.END).strip()
        if not urls_text:
            messagebox.showerror("Error", "Please enter URLs for batch download")
            return
        
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

        # Validate each URL as an image URL
        valid_urls = []
        invalid_urls = []

        for url in urls:
            is_valid, _ = ImageURLValidator.validate_image_url(url)
            if is_valid:
                valid_urls.append(url)
            else:
                invalid_urls.append(url)

        if not valid_urls:
            messagebox.showerror("Error", "No valid image URLs found")
            return

        if invalid_urls:
            invalid_count = len(invalid_urls)
            if not messagebox.askyesno(
                "Warning",
                f"{invalid_count} invalid image URLs found. Continue with {len(valid_urls)} valid image URLs?"
            ):
                return
        
        destination = self.dest_var.get().strip()
        if not destination:
            destination = config.get("download_directory", "./downloads")
        
        try:
            download_ids = self.download_manager.add_batch_downloads(
                valid_urls, destination=destination, progress_callback=self.update_download_progress
            )
            self.status_var.set(f"Started {len(download_ids)} downloads")
            self.batch_text.delete("1.0", tk.END)  # Clear batch text
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start batch downloads: {e}")
    
    def update_download_progress(self, download_info: Dict[str, Any]):
        """Update download progress in GUI."""
        download_id = download_info.get('download_id', '')
        
        if download_id not in self.download_frames:
            # Create new progress frame
            frame = DownloadProgressFrame(self.downloads_scrollable_frame, download_info)
            frame.grid(sticky='ew', padx=5, pady=2)
            self.download_frames[download_id] = frame
            
            # Configure grid weight
            self.downloads_scrollable_frame.columnconfigure(0, weight=1)
        else:
            # Update existing frame
            self.download_frames[download_id].update_progress(download_info)
        
        # Update canvas scroll region
        self.downloads_canvas.configure(scrollregion=self.downloads_canvas.bbox("all"))
    
    def start_update_thread(self):
        """Start background update thread."""
        def update_worker():
            while True:
                try:
                    # Update queue status
                    queue_status = self.download_manager.get_queue_status()
                    status_text = f"Queue: {queue_status['active_downloads']} active, {queue_status['queued_downloads']} pending"
                    self.queue_status_var.set(status_text)
                    
                    # Update download progress for all active downloads
                    all_downloads = self.download_manager.get_all_downloads()
                    for download_info in all_downloads:
                        self.update_download_progress(download_info)
                    
                except Exception as e:
                    logger.error(f"Update thread error: {e}")
                
                threading.Event().wait(1)  # Update every second
        
        update_thread = threading.Thread(target=update_worker, daemon=True)
        update_thread.start()
    
    def on_closing(self):
        """Handle window closing."""
        if messagebox.askokcancel("Quit", "Do you want to quit? Active downloads will be cancelled."):
            self.download_manager.shutdown()
            self.root.destroy()
    
    def create_history_tab(self):
        """Create history tab content."""
        # History controls
        history_controls = ttk.Frame(self.history_frame)
        history_controls.grid(row=0, column=0, sticky='ew', padx=5, pady=5)

        ttk.Button(history_controls, text="Refresh", command=self.refresh_history).grid(row=0, column=0, padx=5)
        ttk.Button(history_controls, text="Clear History", command=self.clear_history).grid(row=0, column=1, padx=5)
        ttk.Button(history_controls, text="Export", command=self.export_history).grid(row=0, column=2, padx=5)

        # History list
        self.history_tree = ttk.Treeview(self.history_frame, columns=('Status', 'Size', 'Date'), show='tree headings')
        self.history_tree.heading('#0', text='Filename')
        self.history_tree.heading('Status', text='Status')
        self.history_tree.heading('Size', text='Size')
        self.history_tree.heading('Date', text='Date')

        self.history_tree.grid(row=1, column=0, sticky='nsew', padx=5, pady=5)

        # History scrollbar
        history_scrollbar = ttk.Scrollbar(self.history_frame, orient='vertical', command=self.history_tree.yview)
        history_scrollbar.grid(row=1, column=1, sticky='ns', pady=5)
        self.history_tree.config(yscrollcommand=history_scrollbar.set)

        self.history_frame.columnconfigure(0, weight=1)
        self.history_frame.rowconfigure(1, weight=1)

    def create_settings_tab(self):
        """Create settings tab content."""
        # Download settings
        download_group = ttk.LabelFrame(self.settings_frame, text="Download Settings")
        download_group.grid(row=0, column=0, sticky='ew', padx=5, pady=5)

        ttk.Label(download_group, text="Max Concurrent Downloads:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.max_downloads_var = tk.IntVar(value=config.get("max_concurrent_downloads", 3))
        ttk.Spinbox(download_group, from_=1, to=10, textvariable=self.max_downloads_var, width=10).grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(download_group, text="Timeout (seconds):").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.timeout_var = tk.IntVar(value=config.get("timeout", 30))
        ttk.Spinbox(download_group, from_=10, to=300, textvariable=self.timeout_var, width=10).grid(row=1, column=1, padx=5, pady=2)

        # UI settings
        ui_group = ttk.LabelFrame(self.settings_frame, text="Interface Settings")
        ui_group.grid(row=1, column=0, sticky='ew', padx=5, pady=5)

        ttk.Label(ui_group, text="Theme:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.theme_var = tk.StringVar(value=config.get("theme", "arc"))
        theme_combo = ttk.Combobox(ui_group, textvariable=self.theme_var, values=['arc', 'equilux', 'adapta'], width=15)
        theme_combo.grid(row=0, column=1, padx=5, pady=2)

        # Save settings button
        ttk.Button(self.settings_frame, text="Save Settings", command=self.save_settings).grid(row=2, column=0, pady=10)

        self.settings_frame.columnconfigure(0, weight=1)

    def refresh_history(self):
        """Refresh download history."""
        # Clear existing items
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # Load history from download manager
        history = self.download_manager.history.history
        for entry in reversed(history[-50:]):  # Show last 50 entries
            filename = entry.get('filename', 'Unknown')
            status = entry.get('status', 'Unknown')
            size = entry.get('file_size', 0)
            size_mb = f"{size / (1024 * 1024):.1f} MB" if size > 0 else "Unknown"
            date = entry.get('timestamp', '')[:19] if entry.get('timestamp') else 'Unknown'

            self.history_tree.insert('', 0, text=filename, values=(status, size_mb, date))

    def clear_history(self):
        """Clear download history."""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear the download history?"):
            self.download_manager.history.history.clear()
            self.download_manager.history.save_history()
            self.refresh_history()

    def export_history(self):
        """Export download history."""
        filename = filedialog.asksaveasfilename(
            title="Export History",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("CSV files", "*.csv")]
        )
        if filename:
            format_type = "json" if filename.endswith('.json') else "csv"
            if self.file_handler.export_download_list(filename, format_type):
                messagebox.showinfo("Success", f"History exported to {filename}")
            else:
                messagebox.showerror("Error", "Failed to export history")

    def save_settings(self):
        """Save application settings."""
        config.update_settings(
            max_concurrent_downloads=self.max_downloads_var.get(),
            timeout=self.timeout_var.get(),
            theme=self.theme_var.get()
        )
        messagebox.showinfo("Settings", "Settings saved successfully!")

    def run(self):
        """Run the application."""
        self.refresh_history()  # Load initial history
        self.root.mainloop()
