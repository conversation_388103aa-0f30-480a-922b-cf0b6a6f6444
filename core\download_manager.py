"""Image download manager for handling multiple image downloads and queue management."""

import json
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor

from core.downloader import FileDownloader, DownloadStatus
from utils.config import config
from utils.logger import logger


class ImageDownloadHistory:
    """Manages download history and statistics."""
    
    def __init__(self, history_file: str = "data/download_history.json"):
        self.history_file = Path(history_file)
        self.history_file.parent.mkdir(parents=True, exist_ok=True)
        self.history = self._load_history()
    
    def _load_history(self) -> List[Dict[str, Any]]:
        """Load download history from file."""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return []
        return []
    
    def save_history(self) -> None:
        """Save download history to file."""
        try:
            # Limit history size
            max_history = config.get("download_history_limit", 1000)
            if len(self.history) > max_history:
                self.history = self.history[-max_history:]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, indent=2, default=str)
        except IOError as e:
            logger.error(f"Failed to save download history: {e}")
    
    def add_download(self, download_info: Dict[str, Any]) -> None:
        """Add a download to history."""
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'url': download_info.get('url'),
            'filename': download_info.get('filename'),
            'status': download_info.get('status'),
            'file_size': download_info.get('total_bytes', 0),
            'download_time': download_info.get('download_time', 0),
            'error_message': download_info.get('error_message', '')
        }
        self.history.append(history_entry)
        self.save_history()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get download statistics."""
        total_downloads = len(self.history)
        successful_downloads = len([h for h in self.history if h['status'] == DownloadStatus.COMPLETED])
        failed_downloads = len([h for h in self.history if h['status'] == DownloadStatus.FAILED])
        total_size = sum(h.get('file_size', 0) for h in self.history if h['status'] == DownloadStatus.COMPLETED)
        
        return {
            'total_downloads': total_downloads,
            'successful_downloads': successful_downloads,
            'failed_downloads': failed_downloads,
            'success_rate': (successful_downloads / total_downloads * 100) if total_downloads > 0 else 0,
            'total_downloaded_bytes': total_size,
            'total_downloaded_mb': total_size / (1024 * 1024)
        }


class ScheduledDownload:
    """Represents a scheduled download."""
    
    def __init__(self, url: str, filename: Optional[str] = None,
                 destination: Optional[str] = None, schedule_time: datetime = None):
        self.url = url
        self.filename = filename
        self.destination = destination
        self.schedule_time = schedule_time or datetime.now()
        self.executed = False


class DownloadManager:
    """Manages multiple downloads, queuing, and scheduling."""
    
    def __init__(self):
        self.downloader = FileDownloader()
        self.download_queue = []
        self.scheduled_downloads = []
        self.history = ImageDownloadHistory()
        self.executor = ThreadPoolExecutor(max_workers=config.get("max_concurrent_downloads", 3))
        self.active_downloads = {}
        self.queue_lock = threading.Lock()
        self.progress_callbacks = {}
        self.scheduler_running = False
        self.scheduler_thread = None
        
        # Start scheduler if enabled
        if config.get("enable_scheduling", True):
            self.start_scheduler()
    
    def add_download(self, url: str, filename: Optional[str] = None,
                    destination: Optional[str] = None,
                    progress_callback: Optional[Callable] = None,
                    start_immediately: bool = True) -> str:
        """Add a download to the queue."""
        download_id = self.downloader.add_download(url, filename, destination, progress_callback)
        
        with self.queue_lock:
            self.download_queue.append(download_id)
        
        if progress_callback:
            self.progress_callbacks[download_id] = progress_callback
        
        if start_immediately:
            self.process_queue()
        
        return download_id
    
    def add_batch_downloads(self, urls: List[str], destination: Optional[str] = None,
                          progress_callback: Optional[Callable] = None) -> List[str]:
        """Add multiple downloads to the queue."""
        download_ids = []
        
        for url in urls:
            try:
                download_id = self.downloader.add_download(url, None, destination, progress_callback)
                download_ids.append(download_id)
                
                with self.queue_lock:
                    self.download_queue.append(download_id)
                
                if progress_callback:
                    self.progress_callbacks[download_id] = progress_callback
                    
            except Exception as e:
                logger.error(f"Failed to add download for {url}: {e}")
        
        self.process_queue()
        return download_ids
    
    def schedule_download(self, url: str, schedule_time: datetime,
                         filename: Optional[str] = None,
                         destination: Optional[str] = None) -> None:
        """Schedule a download for later execution."""
        scheduled_download = ScheduledDownload(url, filename, destination, schedule_time)
        self.scheduled_downloads.append(scheduled_download)
        logger.info(f"Scheduled download: {url} for {schedule_time}")
    
    def process_queue(self) -> None:
        """Process the download queue."""
        max_concurrent = config.get("max_concurrent_downloads", 3)
        
        with self.queue_lock:
            # Count active downloads
            active_count = len([d for d in self.active_downloads.values() 
                              if d.get('status') == DownloadStatus.DOWNLOADING])
            
            # Start new downloads if slots available
            while (active_count < max_concurrent and self.download_queue):
                download_id = self.download_queue.pop(0)
                
                if download_id in self.downloader.active_downloads:
                    future = self.executor.submit(self._download_with_callback, download_id)
                    self.active_downloads[download_id] = {
                        'future': future,
                        'status': DownloadStatus.DOWNLOADING
                    }
                    active_count += 1
    
    def _download_with_callback(self, download_id: str) -> None:
        """Download with completion callback."""
        try:
            self.downloader.start_download(download_id)
            
            # Wait for download to complete
            download_item = self.downloader.active_downloads[download_id]
            while download_item.status in [DownloadStatus.DOWNLOADING, DownloadStatus.PAUSED]:
                time.sleep(0.5)
            
            # Add to history
            download_info = download_item.get_info()
            if download_item.start_time and download_item.end_time:
                download_info['download_time'] = download_item.end_time - download_item.start_time
            
            self.history.add_download(download_info)
            
        except Exception as e:
            logger.error(f"Download failed: {e}")
        
        finally:
            # Remove from active downloads
            with self.queue_lock:
                if download_id in self.active_downloads:
                    del self.active_downloads[download_id]
            
            # Process next in queue
            self.process_queue()
    
    def pause_download(self, download_id: str) -> bool:
        """Pause a specific download."""
        return self.downloader.pause_download(download_id)
    
    def resume_download(self, download_id: str) -> bool:
        """Resume a paused download."""
        return self.downloader.resume_download(download_id)
    
    def cancel_download(self, download_id: str) -> bool:
        """Cancel a specific download."""
        success = self.downloader.cancel_download(download_id)
        
        if success:
            # Remove from queue if present
            with self.queue_lock:
                if download_id in self.download_queue:
                    self.download_queue.remove(download_id)
                
                if download_id in self.active_downloads:
                    # Cancel the future
                    future = self.active_downloads[download_id].get('future')
                    if future:
                        future.cancel()
                    del self.active_downloads[download_id]
        
        return success
    
    def pause_all_downloads(self) -> None:
        """Pause all active downloads."""
        for download_id in list(self.active_downloads.keys()):
            self.pause_download(download_id)
    
    def resume_all_downloads(self) -> None:
        """Resume all paused downloads."""
        for download_id in self.downloader.active_downloads:
            download_item = self.downloader.active_downloads[download_id]
            if download_item.status == DownloadStatus.PAUSED:
                self.resume_download(download_id)
    
    def cancel_all_downloads(self) -> None:
        """Cancel all downloads."""
        for download_id in list(self.active_downloads.keys()):
            self.cancel_download(download_id)
        
        with self.queue_lock:
            self.download_queue.clear()
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status."""
        with self.queue_lock:
            return {
                'queued_downloads': len(self.download_queue),
                'active_downloads': len(self.active_downloads),
                'scheduled_downloads': len([s for s in self.scheduled_downloads if not s.executed])
            }
    
    def get_all_downloads(self) -> List[Dict[str, Any]]:
        """Get information about all downloads."""
        downloads = []
        
        for download_id, download_item in self.downloader.active_downloads.items():
            info = download_item.get_info()
            info['download_id'] = download_id
            downloads.append(info)
        
        return downloads
    
    def start_scheduler(self) -> None:
        """Start the download scheduler."""
        if not self.scheduler_running:
            self.scheduler_running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_worker, daemon=True)
            self.scheduler_thread.start()
            logger.info("Download scheduler started")
    
    def stop_scheduler(self) -> None:
        """Stop the download scheduler."""
        self.scheduler_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=1)
        logger.info("Download scheduler stopped")
    
    def _scheduler_worker(self) -> None:
        """Worker thread for scheduled downloads."""
        while self.scheduler_running:
            current_time = datetime.now()
            
            for scheduled_download in self.scheduled_downloads[:]:
                if (not scheduled_download.executed and 
                    scheduled_download.schedule_time <= current_time):
                    
                    try:
                        self.add_download(
                            scheduled_download.url,
                            scheduled_download.filename,
                            scheduled_download.destination
                        )
                        scheduled_download.executed = True
                        logger.info(f"Executed scheduled download: {scheduled_download.url}")
                    except Exception as e:
                        logger.error(f"Failed to execute scheduled download: {e}")
            
            # Clean up executed scheduled downloads
            self.scheduled_downloads = [s for s in self.scheduled_downloads if not s.executed]
            
            time.sleep(60)  # Check every minute
    
    def shutdown(self) -> None:
        """Shutdown the download manager."""
        self.stop_scheduler()
        self.cancel_all_downloads()
        self.executor.shutdown(wait=True)
