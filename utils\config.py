"""Configuration management for the file downloader application."""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigManager:
    """Manages application configuration settings."""
    
    def __init__(self, config_file: str = "config/settings.json"):
        self.config_file = Path(config_file)
        self.settings = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default if not exists."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading config: {e}. Using defaults.")
                return self._get_default_config()
        else:
            # Create config directory if it doesn't exist
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            default_config = self._get_default_config()
            self.save_config(default_config)
            return default_config
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Return default configuration settings."""
        return {
            "download_directory": "./downloads",
            "max_concurrent_downloads": 3,
            "chunk_size": 8192,
            "timeout": 30,
            "max_retries": 3,
            "retry_delay": 2,
            "enable_logging": True,
            "log_level": "INFO",
            "verify_ssl": True,
            "user_agent": "FileDownloader/1.0",
            "theme": "arc",
            "window_geometry": "800x600",
            "auto_detect_filename": True,
            "show_notifications": True,
            "bandwidth_limit": 0,
            "enable_checksum_verification": True,
            "download_history_limit": 1000,
            "auto_retry_failed": True,
            "enable_scheduling": True
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        return self.settings.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set a configuration value."""
        self.settings[key] = value
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> None:
        """Save configuration to file."""
        config_to_save = config or self.settings
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4)
        except IOError as e:
            print(f"Error saving config: {e}")
    
    def update_settings(self, **kwargs) -> None:
        """Update multiple settings at once."""
        self.settings.update(kwargs)
        self.save_config()
    
    def reset_to_defaults(self) -> None:
        """Reset configuration to default values."""
        self.settings = self._get_default_config()
        self.save_config()


# Global config instance
config = ConfigManager()
