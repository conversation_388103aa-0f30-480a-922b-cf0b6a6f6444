#!/usr/bin/env python3
"""
Demo script for Tool Image Downloader

This script demonstrates the core functionality of the Tool Image Downloader
without the GUI interface.
"""

import sys
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.download_manager import DownloadManager
from core.file_handler import ImageFileHandler
from utils.config import config
from utils.logger import logger


def progress_callback(download_info):
    """Callback function to display download progress."""
    filename = download_info.get('filename', 'Unknown')
    progress = download_info.get('progress', 0)
    status = download_info.get('status', 'unknown')
    speed = download_info.get('speed', 0)
    
    if speed > 0:
        if speed > 1024 * 1024:  # MB/s
            speed_text = f"{speed / (1024 * 1024):.1f} MB/s"
        else:  # KB/s
            speed_text = f"{speed / 1024:.1f} KB/s"
    else:
        speed_text = "0 KB/s"
    
    print(f"\r{filename}: {progress:.1f}% [{status}] - {speed_text}", end='', flush=True)
    
    if status in ['completed', 'failed', 'cancelled']:
        print()  # New line when download finishes


def demo_single_image_download():
    """Demonstrate single image download."""
    print("=== Single Image Download Demo ===")

    # Create download manager
    manager = DownloadManager()

    # Test URL (small image file from a reliable source)
    test_url = "https://via.placeholder.com/150x150.png"

    print(f"Downloading image: {test_url}")

    try:
        # Add download
        download_id = manager.add_download(
            test_url,
            destination="./demo_downloads",
            progress_callback=progress_callback
        )
        
        # Wait for download to complete
        while True:
            download_info = manager.downloader.get_download_info(download_id)
            if download_info and download_info['status'] in ['completed', 'failed', 'cancelled']:
                break
            time.sleep(0.5)
        
        final_info = manager.downloader.get_download_info(download_id)
        if final_info['status'] == 'completed':
            print(f"✓ Download completed: {final_info['filename']}")
        else:
            print(f"✗ Download failed: {final_info.get('error_message', 'Unknown error')}")
    
    except Exception as e:
        print(f"✗ Error: {e}")
    
    finally:
        manager.shutdown()


def demo_batch_image_download():
    """Demonstrate batch image downloads."""
    print("\n=== Batch Image Download Demo ===")

    # Create download manager
    manager = DownloadManager()

    # Test URLs (real image files)
    test_urls = [
        "https://via.placeholder.com/200x200.png",
        "https://via.placeholder.com/300x200.jpg",
        "https://via.placeholder.com/250x250.gif"
    ]

    print(f"Downloading {len(test_urls)} images...")
    
    try:
        # Add batch downloads
        download_ids = manager.add_batch_downloads(
            test_urls,
            destination="./demo_downloads",
            progress_callback=progress_callback
        )
        
        print(f"Started {len(download_ids)} downloads")
        
        # Wait for all downloads to complete
        while True:
            all_completed = True
            for download_id in download_ids:
                download_info = manager.downloader.get_download_info(download_id)
                if download_info and download_info['status'] not in ['completed', 'failed', 'cancelled']:
                    all_completed = False
                    break
            
            if all_completed:
                break
            time.sleep(0.5)
        
        # Show results
        print("\nBatch download results:")
        for i, download_id in enumerate(download_ids):
            download_info = manager.downloader.get_download_info(download_id)
            if download_info:
                status = download_info['status']
                filename = download_info['filename']
                if status == 'completed':
                    print(f"  ✓ {filename}")
                else:
                    print(f"  ✗ {filename} - {download_info.get('error_message', 'Failed')}")
    
    except Exception as e:
        print(f"✗ Error: {e}")
    
    finally:
        manager.shutdown()


def demo_image_statistics():
    """Demonstrate image statistics."""
    print("\n=== Image Statistics Demo ===")

    file_handler = ImageFileHandler()

    # Get image statistics
    stats = file_handler.get_image_statistics()

    print("Image Directory Statistics:")
    print(f"  Total images: {stats.get('total_images', 0)}")
    print(f"  Total size: {stats.get('total_size_mb', 0):.2f} MB")
    print(f"  Available space: {stats.get('available_space_bytes', 0) / (1024*1024*1024):.2f} GB")

    format_stats = stats.get('format_statistics', {})
    if format_stats:
        print("\nImages by format:")
        for format_type, format_info in format_stats.items():
            count = format_info.get('count', 0)
            size_mb = format_info.get('size', 0) / (1024 * 1024)
            print(f"  {format_type}: {count} images ({size_mb:.2f} MB)")

    dimension_stats = stats.get('dimension_statistics', {})
    if dimension_stats:
        print("\nImages by size:")
        for size_category, count in dimension_stats.items():
            print(f"  {size_category}: {count} images")


def main():
    """Main demo function."""
    print("Tool Image Downloader - Demo")
    print("=" * 40)

    # Create demo downloads directory
    Path("./demo_downloads").mkdir(exist_ok=True)

    try:
        # Run demos
        demo_single_image_download()
        demo_batch_image_download()
        demo_image_statistics()

        print("\n" + "=" * 40)
        print("Demo completed successfully!")
        print("Check the './demo_downloads' directory for downloaded images.")
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo error: {e}")
        logger.error(f"Demo error: {e}")


if __name__ == "__main__":
    main()
