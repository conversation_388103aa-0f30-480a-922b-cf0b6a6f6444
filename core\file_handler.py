"""Image file handling utilities for the image downloader application."""

import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any

from utils.config import config
from utils.logger import logger
from utils.validators import ImageFileValidator


class ImageFileHandler:
    """Handles image file operations and management."""
    
    def __init__(self):
        self.download_directory = Path(config.get("download_directory", "./downloads"))
        self.download_directory.mkdir(parents=True, exist_ok=True)
    
    def create_download_directory(self, path: str) -> bool:
        """Create download directory if it doesn't exist."""
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Failed to create directory {path}: {e}")
            return False
    
    def get_available_space(self, path: str) -> int:
        """Get available disk space in bytes."""
        try:
            return shutil.disk_usage(path).free
        except Exception:
            return 0
    
    def check_disk_space(self, path: str, required_bytes: int) -> bool:
        """Check if there's enough disk space for download."""
        available = self.get_available_space(path)
        return available >= required_bytes
    
    def move_file(self, source: Path, destination: Path) -> bool:
        """Move file from source to destination."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(destination))
            logger.info(f"Moved file from {source} to {destination}")
            return True
        except Exception as e:
            logger.error(f"Failed to move file: {e}")
            return False
    
    def copy_file(self, source: Path, destination: Path) -> bool:
        """Copy file from source to destination."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(str(source), str(destination))
            logger.info(f"Copied file from {source} to {destination}")
            return True
        except Exception as e:
            logger.error(f"Failed to copy file: {e}")
            return False
    
    def delete_file(self, file_path: Path) -> bool:
        """Delete a file."""
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file: {e}")
            return False
    
    def get_image_info(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Get detailed image information including dimensions."""
        return ImageFileValidator.get_image_info(file_path)
    
    def list_images(self, directory: Optional[str] = None) -> List[Dict[str, Any]]:
        """List all downloaded image files."""
        if directory is None:
            directory = self.download_directory
        else:
            directory = Path(directory)

        images = []
        try:
            if directory.exists():
                for file_path in directory.iterdir():
                    if file_path.is_file():
                        image_info = self.get_image_info(file_path)
                        if image_info:  # Only include valid images
                            images.append(image_info)
        except Exception as e:
            logger.error(f"Failed to list images: {e}")

        return sorted(images, key=lambda x: x['modified'], reverse=True)
    
    def organize_images(self, organize_by: str = "format") -> bool:
        """Organize images into subdirectories."""
        try:
            images = self.list_images()

            for image_info in images:
                file_path = Path(image_info['path'])

                if organize_by == "format":
                    # Organize by image format
                    extension = image_info['extension'].lstrip('.')
                    target_dir = self.download_directory / extension.upper()
                elif organize_by == "date":
                    # Organize by download date
                    from datetime import datetime
                    date = datetime.fromtimestamp(image_info['modified'])
                    target_dir = self.download_directory / date.strftime("%Y-%m")
                elif organize_by == "size":
                    # Organize by image dimensions
                    width = image_info.get('width')
                    height = image_info.get('height')
                    if width and height:
                        if width >= 1920 or height >= 1080:
                            target_dir = self.download_directory / "HD"
                        elif width >= 1280 or height >= 720:
                            target_dir = self.download_directory / "Medium"
                        else:
                            target_dir = self.download_directory / "Small"
                    else:
                        target_dir = self.download_directory / "Unknown_Size"
                else:
                    continue

                target_dir.mkdir(exist_ok=True)
                target_path = target_dir / file_path.name

                if not target_path.exists():
                    self.move_file(file_path, target_path)

            logger.info(f"Images organized by {organize_by}")
            return True

        except Exception as e:
            logger.error(f"Failed to organize images: {e}")
            return False
    
    def cleanup_invalid_images(self) -> int:
        """Clean up invalid or corrupted image files."""
        cleaned_count = 0

        try:
            for file_path in self.download_directory.rglob("*"):
                if file_path.is_file():
                    # Check for temporary files, very small files, or invalid images
                    if (file_path.name.startswith('.') or
                        file_path.suffix in ['.tmp', '.part', '.crdownload'] or
                        file_path.stat().st_size < 100 or  # Less than 100 bytes
                        not ImageFileValidator.is_valid_image_file(file_path)):

                        if self.delete_file(file_path):
                            cleaned_count += 1

            logger.info(f"Cleaned up {cleaned_count} invalid image files")

        except Exception as e:
            logger.error(f"Failed to cleanup invalid images: {e}")

        return cleaned_count
    
    def get_image_statistics(self) -> Dict[str, Any]:
        """Get image download directory statistics."""
        try:
            images = self.list_images()
            total_images = len(images)
            total_size = sum(img['size'] for img in images)

            # Group by image format
            format_stats = {}
            dimension_stats = {'HD': 0, 'Medium': 0, 'Small': 0, 'Unknown': 0}

            for image_info in images:
                # Format statistics
                extension = image_info['extension'].lstrip('.').upper()
                if extension not in format_stats:
                    format_stats[extension] = {'count': 0, 'size': 0}
                format_stats[extension]['count'] += 1
                format_stats[extension]['size'] += image_info['size']

                # Dimension statistics
                width = image_info.get('width')
                height = image_info.get('height')
                if width and height:
                    if width >= 1920 or height >= 1080:
                        dimension_stats['HD'] += 1
                    elif width >= 1280 or height >= 720:
                        dimension_stats['Medium'] += 1
                    else:
                        dimension_stats['Small'] += 1
                else:
                    dimension_stats['Unknown'] += 1

            return {
                'total_images': total_images,
                'total_size_bytes': total_size,
                'total_size_mb': total_size / (1024 * 1024),
                'total_size_gb': total_size / (1024 * 1024 * 1024),
                'format_statistics': format_stats,
                'dimension_statistics': dimension_stats,
                'download_directory': str(self.download_directory),
                'available_space_bytes': self.get_available_space(str(self.download_directory))
            }

        except Exception as e:
            logger.error(f"Failed to get image statistics: {e}")
            return {}
    
    def search_images(self, query: str) -> List[Dict[str, Any]]:
        """Search downloaded images by name."""
        try:
            images = self.list_images()
            query_lower = query.lower()

            matching_images = []
            for image_info in images:
                if query_lower in image_info['name'].lower():
                    matching_images.append(image_info)

            return matching_images

        except Exception as e:
            logger.error(f"Failed to search images: {e}")
            return []

    def export_image_list(self, export_path: str, format: str = "json") -> bool:
        """Export image list to file."""
        try:
            images = self.list_images()
            export_file = Path(export_path)

            if format.lower() == "json":
                import json
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(images, f, indent=2, default=str)

            elif format.lower() == "csv":
                import csv
                with open(export_file, 'w', newline='', encoding='utf-8') as f:
                    if images:
                        writer = csv.DictWriter(f, fieldnames=images[0].keys())
                        writer.writeheader()
                        writer.writerows(images)

            logger.info(f"Exported image list to {export_file}")
            return True

        except Exception as e:
            logger.error(f"Failed to export image list: {e}")
            return False
