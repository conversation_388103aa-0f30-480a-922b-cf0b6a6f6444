"""File handling utilities for the download application."""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any
import mimetypes

from utils.config import config
from utils.logger import logger
from utils.validators import FileValidator


class FileHandler:
    """Handles file operations and management."""
    
    def __init__(self):
        self.download_directory = Path(config.get("download_directory", "./downloads"))
        self.download_directory.mkdir(parents=True, exist_ok=True)
    
    def create_download_directory(self, path: str) -> bool:
        """Create download directory if it doesn't exist."""
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Failed to create directory {path}: {e}")
            return False
    
    def get_available_space(self, path: str) -> int:
        """Get available disk space in bytes."""
        try:
            return shutil.disk_usage(path).free
        except Exception:
            return 0
    
    def check_disk_space(self, path: str, required_bytes: int) -> bool:
        """Check if there's enough disk space for download."""
        available = self.get_available_space(path)
        return available >= required_bytes
    
    def move_file(self, source: Path, destination: Path) -> bool:
        """Move file from source to destination."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(destination))
            logger.info(f"Moved file from {source} to {destination}")
            return True
        except Exception as e:
            logger.error(f"Failed to move file: {e}")
            return False
    
    def copy_file(self, source: Path, destination: Path) -> bool:
        """Copy file from source to destination."""
        try:
            destination.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(str(source), str(destination))
            logger.info(f"Copied file from {source} to {destination}")
            return True
        except Exception as e:
            logger.error(f"Failed to copy file: {e}")
            return False
    
    def delete_file(self, file_path: Path) -> bool:
        """Delete a file."""
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file: {e}")
            return False
    
    def get_file_info(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Get detailed file information."""
        try:
            if not file_path.exists():
                return None
            
            stat = file_path.stat()
            mime_type, _ = mimetypes.guess_type(str(file_path))
            
            return {
                'name': file_path.name,
                'path': str(file_path),
                'size': stat.st_size,
                'size_mb': stat.st_size / (1024 * 1024),
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'mime_type': mime_type,
                'extension': file_path.suffix.lower(),
                'is_file': file_path.is_file(),
                'is_directory': file_path.is_dir()
            }
        except Exception as e:
            logger.error(f"Failed to get file info: {e}")
            return None
    
    def list_downloads(self, directory: Optional[str] = None) -> List[Dict[str, Any]]:
        """List all downloaded files."""
        if directory is None:
            directory = self.download_directory
        else:
            directory = Path(directory)
        
        files = []
        try:
            if directory.exists():
                for file_path in directory.iterdir():
                    if file_path.is_file():
                        file_info = self.get_file_info(file_path)
                        if file_info:
                            files.append(file_info)
        except Exception as e:
            logger.error(f"Failed to list downloads: {e}")
        
        return sorted(files, key=lambda x: x['modified'], reverse=True)
    
    def organize_downloads(self, organize_by: str = "type") -> bool:
        """Organize downloads into subdirectories."""
        try:
            files = self.list_downloads()
            
            for file_info in files:
                file_path = Path(file_info['path'])
                
                if organize_by == "type":
                    # Organize by file type
                    category = self._get_file_category(file_info['extension'])
                    target_dir = self.download_directory / category
                elif organize_by == "date":
                    # Organize by download date
                    from datetime import datetime
                    date = datetime.fromtimestamp(file_info['created'])
                    target_dir = self.download_directory / date.strftime("%Y-%m")
                else:
                    continue
                
                target_dir.mkdir(exist_ok=True)
                target_path = target_dir / file_path.name
                
                if not target_path.exists():
                    self.move_file(file_path, target_path)
            
            logger.info(f"Downloads organized by {organize_by}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to organize downloads: {e}")
            return False
    
    def _get_file_category(self, extension: str) -> str:
        """Get file category based on extension."""
        categories = {
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico'],
            'videos': ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v'],
            'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'],
            'documents': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', 
                         '.txt', '.rtf', '.odt', '.ods', '.odp'],
            'archives': ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'],
            'executables': ['.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg'],
            'code': ['.py', '.js', '.html', '.css', '.json', '.xml', '.sql']
        }
        
        for category, extensions in categories.items():
            if extension.lower() in extensions:
                return category
        
        return 'others'
    
    def cleanup_incomplete_downloads(self) -> int:
        """Clean up incomplete or corrupted download files."""
        cleaned_count = 0
        
        try:
            for file_path in self.download_directory.rglob("*"):
                if file_path.is_file():
                    # Check for temporary files or very small files that might be incomplete
                    if (file_path.name.startswith('.') or 
                        file_path.suffix in ['.tmp', '.part', '.crdownload'] or
                        file_path.stat().st_size < 100):  # Less than 100 bytes
                        
                        if self.delete_file(file_path):
                            cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} incomplete download files")
            
        except Exception as e:
            logger.error(f"Failed to cleanup incomplete downloads: {e}")
        
        return cleaned_count
    
    def get_download_statistics(self) -> Dict[str, Any]:
        """Get download directory statistics."""
        try:
            files = self.list_downloads()
            total_files = len(files)
            total_size = sum(f['size'] for f in files)
            
            # Group by file type
            type_stats = {}
            for file_info in files:
                category = self._get_file_category(file_info['extension'])
                if category not in type_stats:
                    type_stats[category] = {'count': 0, 'size': 0}
                type_stats[category]['count'] += 1
                type_stats[category]['size'] += file_info['size']
            
            return {
                'total_files': total_files,
                'total_size_bytes': total_size,
                'total_size_mb': total_size / (1024 * 1024),
                'total_size_gb': total_size / (1024 * 1024 * 1024),
                'type_statistics': type_stats,
                'download_directory': str(self.download_directory),
                'available_space_bytes': self.get_available_space(str(self.download_directory))
            }
            
        except Exception as e:
            logger.error(f"Failed to get download statistics: {e}")
            return {}
    
    def search_downloads(self, query: str) -> List[Dict[str, Any]]:
        """Search downloaded files by name."""
        try:
            files = self.list_downloads()
            query_lower = query.lower()
            
            matching_files = []
            for file_info in files:
                if query_lower in file_info['name'].lower():
                    matching_files.append(file_info)
            
            return matching_files
            
        except Exception as e:
            logger.error(f"Failed to search downloads: {e}")
            return []
    
    def export_download_list(self, export_path: str, format: str = "json") -> bool:
        """Export download list to file."""
        try:
            files = self.list_downloads()
            export_file = Path(export_path)
            
            if format.lower() == "json":
                import json
                with open(export_file, 'w', encoding='utf-8') as f:
                    json.dump(files, f, indent=2, default=str)
            
            elif format.lower() == "csv":
                import csv
                with open(export_file, 'w', newline='', encoding='utf-8') as f:
                    if files:
                        writer = csv.DictWriter(f, fieldnames=files[0].keys())
                        writer.writeheader()
                        writer.writerows(files)
            
            logger.info(f"Exported download list to {export_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export download list: {e}")
            return False
