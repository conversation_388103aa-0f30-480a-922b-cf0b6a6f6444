"""Validation utilities for image URLs and file operations."""

import re
import mimetypes
from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from urllib.parse import urlparse, unquote
import validators
import requests


class ImageURLValidator:
    """Validates and processes URLs specifically for image downloading."""

    # Supported image file extensions and their MIME types
    SUPPORTED_IMAGE_EXTENSIONS = {
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico'
    }

    # MIME types for image files
    IMAGE_MIME_TYPES = {
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
        'image/bmp', 'image/webp', 'image/svg+xml', 'image/x-icon',
        'image/vnd.microsoft.icon'
    }
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """Check if URL is valid."""
        try:
            return validators.url(url) is True
        except Exception:
            return False
    
    @staticmethod
    def extract_filename_from_url(url: str) -> Optional[str]:
        """Extract filename from URL."""
        try:
            parsed_url = urlparse(url)
            path = unquote(parsed_url.path)
            
            if path and path != '/':
                filename = Path(path).name
                if filename and '.' in filename:
                    return filename
            
            # If no filename found, try to extract from query parameters
            if parsed_url.query:
                # Look for common filename parameters
                for param in ['filename', 'file', 'name']:
                    if param in parsed_url.query:
                        match = re.search(f'{param}=([^&]+)', parsed_url.query)
                        if match:
                            return unquote(match.group(1))
            
            return None
        except Exception:
            return None
    
    @staticmethod
    def get_file_extension_from_url(url: str) -> Optional[str]:
        """Get file extension from URL."""
        filename = ImageURLValidator.extract_filename_from_url(url)
        if filename:
            return Path(filename).suffix.lower()
        return None

    @staticmethod
    def is_image_url(url: str) -> bool:
        """Check if the URL points to an image file."""
        extension = ImageURLValidator.get_file_extension_from_url(url)
        return extension in ImageURLValidator.SUPPORTED_IMAGE_EXTENSIONS if extension else False

    @staticmethod
    def validate_image_url(url: str) -> Tuple[bool, str]:
        """Validate if URL is valid and points to an image file."""
        if not ImageURLValidator.is_valid_url(url):
            return False, "Invalid URL format"

        # First check if URL has image extension
        if ImageURLValidator.is_image_url(url):
            return True, "Valid image URL (by extension)"

        # If no extension or non-image extension, check HTTP headers
        try:
            is_image, message, content_type = ImageURLValidator.check_image_url_headers(url, timeout=5)
            if is_image:
                return True, f"Valid image URL (content-type: {content_type})"
            else:
                extension = ImageURLValidator.get_file_extension_from_url(url)
                if extension:
                    return False, f"File type '{extension}' is not a supported image format. Supported formats: {', '.join(sorted(ImageURLValidator.SUPPORTED_IMAGE_EXTENSIONS))}"
                else:
                    return False, f"URL does not serve image content. {message}. Supported formats: {', '.join(sorted(ImageURLValidator.SUPPORTED_IMAGE_EXTENSIONS))}"
        except Exception as e:
            return False, f"Could not validate URL: {str(e)}"

    @staticmethod
    def check_image_url_headers(url: str, timeout: int = 10) -> Tuple[bool, str, Optional[str]]:
        """Check if URL actually serves an image by examining HTTP headers."""
        try:
            response = requests.head(url, timeout=timeout, allow_redirects=True)
            content_type = response.headers.get('content-type', '').lower()

            if any(mime_type in content_type for mime_type in ImageURLValidator.IMAGE_MIME_TYPES):
                return True, "Valid image content type", content_type
            else:
                return False, f"Content type '{content_type}' is not an image", content_type

        except requests.RequestException as e:
            return False, f"Failed to check URL: {str(e)}", None

    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for safe file system operations."""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        # Ensure filename is not empty
        if not filename:
            filename = "download"
        
        # Limit filename length
        if len(filename) > 255:
            name, ext = Path(filename).stem, Path(filename).suffix
            max_name_length = 255 - len(ext)
            filename = name[:max_name_length] + ext
        
        return filename
    
    @staticmethod
    def generate_unique_filename(directory: Path, filename: str) -> str:
        """Generate unique filename if file already exists."""
        file_path = directory / filename
        
        if not file_path.exists():
            return filename
        
        name = Path(filename).stem
        extension = Path(filename).suffix
        counter = 1
        
        while True:
            new_filename = f"{name}_{counter}{extension}"
            new_path = directory / new_filename
            if not new_path.exists():
                return new_filename
            counter += 1


class ImageFileValidator:
    """Validates image file operations and integrity."""
    
    @staticmethod
    def is_valid_download_path(path: str) -> bool:
        """Check if download path is valid."""
        try:
            path_obj = Path(path)
            # Check if parent directory exists or can be created
            if path_obj.parent.exists():
                return True
            else:
                # Try to create parent directories
                path_obj.parent.mkdir(parents=True, exist_ok=True)
                return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_size(file_path: Path) -> int:
        """Get file size in bytes."""
        try:
            return file_path.stat().st_size
        except Exception:
            return 0
    
    @staticmethod
    def calculate_checksum(file_path: Path, algorithm: str = 'md5') -> Optional[str]:
        """Calculate file checksum."""
        import hashlib
        
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception:
            return None
    
    @staticmethod
    def verify_file_integrity(file_path: Path, expected_size: Optional[int] = None,
                            expected_checksum: Optional[str] = None,
                            checksum_algorithm: str = 'md5') -> bool:
        """Verify file integrity using size and/or checksum."""
        if not file_path.exists():
            return False
        
        # Check file size
        if expected_size is not None:
            actual_size = ImageFileValidator.get_file_size(file_path)
            if actual_size != expected_size:
                return False

        # Check checksum
        if expected_checksum is not None:
            actual_checksum = ImageFileValidator.calculate_checksum(file_path, checksum_algorithm)
            if actual_checksum != expected_checksum.lower():
                return False
        
        return True

    @staticmethod
    def is_valid_image_file(file_path: Path) -> bool:
        """Check if file is a valid image by examining its content."""
        try:
            # Check file extension first
            extension = file_path.suffix.lower()
            if extension not in ImageURLValidator.SUPPORTED_IMAGE_EXTENSIONS:
                return False

            # Check MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and mime_type.lower() in ImageURLValidator.IMAGE_MIME_TYPES:
                return True

            # Try to validate with PIL if available
            try:
                from PIL import Image
                with Image.open(file_path) as img:
                    img.verify()  # Verify it's a valid image
                return True
            except ImportError:
                # PIL not available, rely on MIME type and extension
                return extension in ImageURLValidator.SUPPORTED_IMAGE_EXTENSIONS
            except Exception:
                return False

        except Exception:
            return False

    @staticmethod
    def get_image_info(file_path: Path) -> Optional[Dict[str, Any]]:
        """Get detailed image information including dimensions."""
        try:
            if not ImageFileValidator.is_valid_image_file(file_path):
                return None

            stat = file_path.stat()
            mime_type, _ = mimetypes.guess_type(str(file_path))

            info = {
                'name': file_path.name,
                'path': str(file_path),
                'size': stat.st_size,
                'size_mb': stat.st_size / (1024 * 1024),
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'mime_type': mime_type,
                'extension': file_path.suffix.lower(),
                'is_image': True,
                'width': None,
                'height': None,
                'format': None
            }

            # Try to get image dimensions with PIL
            try:
                from PIL import Image
                with Image.open(file_path) as img:
                    info['width'] = img.width
                    info['height'] = img.height
                    info['format'] = img.format
            except ImportError:
                pass  # PIL not available
            except Exception:
                pass  # Could not read image dimensions

            return info

        except Exception:
            return None

    @staticmethod
    def cleanup_non_image_files(directory: Path) -> int:
        """Remove non-image files from directory."""
        cleaned_count = 0

        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    if not ImageFileValidator.is_valid_image_file(file_path):
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                        except Exception:
                            pass  # Ignore cleanup errors

        except Exception:
            pass

        return cleaned_count
