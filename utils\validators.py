"""Validation utilities for URLs and file operations."""

import re
import mimetypes
from pathlib import Path
from typing import Op<PERSON>, Tuple, Dict
from urllib.parse import urlparse, unquote
import validators


class URLValidator:
    """Validates and processes URLs for downloading."""
    
    # Common file extensions and their MIME types
    SUPPORTED_EXTENSIONS = {
        # Images
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico',
        # Videos
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v',
        # Audio
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a',
        # Documents
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
        '.rtf', '.odt', '.ods', '.odp',
        # Archives
        '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz',
        # Executables
        '.exe', '.msi', '.deb', '.rpm', '.dmg', '.pkg',
        # Code
        '.py', '.js', '.html', '.css', '.json', '.xml', '.sql',
        # Others
        '.iso', '.bin', '.img'
    }
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """Check if URL is valid."""
        try:
            return validators.url(url) is True
        except Exception:
            return False
    
    @staticmethod
    def extract_filename_from_url(url: str) -> Optional[str]:
        """Extract filename from URL."""
        try:
            parsed_url = urlparse(url)
            path = unquote(parsed_url.path)
            
            if path and path != '/':
                filename = Path(path).name
                if filename and '.' in filename:
                    return filename
            
            # If no filename found, try to extract from query parameters
            if parsed_url.query:
                # Look for common filename parameters
                for param in ['filename', 'file', 'name']:
                    if param in parsed_url.query:
                        match = re.search(f'{param}=([^&]+)', parsed_url.query)
                        if match:
                            return unquote(match.group(1))
            
            return None
        except Exception:
            return None
    
    @staticmethod
    def get_file_extension_from_url(url: str) -> Optional[str]:
        """Get file extension from URL."""
        filename = URLValidator.extract_filename_from_url(url)
        if filename:
            return Path(filename).suffix.lower()
        return None
    
    @staticmethod
    def is_supported_file_type(url: str) -> bool:
        """Check if the file type is supported."""
        extension = URLValidator.get_file_extension_from_url(url)
        return extension in URLValidator.SUPPORTED_EXTENSIONS if extension else True
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for safe file system operations."""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        
        # Ensure filename is not empty
        if not filename:
            filename = "download"
        
        # Limit filename length
        if len(filename) > 255:
            name, ext = Path(filename).stem, Path(filename).suffix
            max_name_length = 255 - len(ext)
            filename = name[:max_name_length] + ext
        
        return filename
    
    @staticmethod
    def generate_unique_filename(directory: Path, filename: str) -> str:
        """Generate unique filename if file already exists."""
        file_path = directory / filename
        
        if not file_path.exists():
            return filename
        
        name = Path(filename).stem
        extension = Path(filename).suffix
        counter = 1
        
        while True:
            new_filename = f"{name}_{counter}{extension}"
            new_path = directory / new_filename
            if not new_path.exists():
                return new_filename
            counter += 1


class FileValidator:
    """Validates file operations and integrity."""
    
    @staticmethod
    def is_valid_download_path(path: str) -> bool:
        """Check if download path is valid."""
        try:
            path_obj = Path(path)
            # Check if parent directory exists or can be created
            if path_obj.parent.exists():
                return True
            else:
                # Try to create parent directories
                path_obj.parent.mkdir(parents=True, exist_ok=True)
                return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_size(file_path: Path) -> int:
        """Get file size in bytes."""
        try:
            return file_path.stat().st_size
        except Exception:
            return 0
    
    @staticmethod
    def calculate_checksum(file_path: Path, algorithm: str = 'md5') -> Optional[str]:
        """Calculate file checksum."""
        import hashlib
        
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception:
            return None
    
    @staticmethod
    def verify_file_integrity(file_path: Path, expected_size: Optional[int] = None,
                            expected_checksum: Optional[str] = None,
                            checksum_algorithm: str = 'md5') -> bool:
        """Verify file integrity using size and/or checksum."""
        if not file_path.exists():
            return False
        
        # Check file size
        if expected_size is not None:
            actual_size = FileValidator.get_file_size(file_path)
            if actual_size != expected_size:
                return False
        
        # Check checksum
        if expected_checksum is not None:
            actual_checksum = FileValidator.calculate_checksum(file_path, checksum_algorithm)
            if actual_checksum != expected_checksum.lower():
                return False
        
        return True
