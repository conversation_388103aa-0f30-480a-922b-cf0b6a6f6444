#!/usr/bin/env python3
"""
Tool Image Downloader - A specialized image downloading application

Features:
- Multi-threaded image downloads with progress tracking
- Pause/Resume functionality for image downloads
- Batch image downloads with validation
- Modern GUI with themes
- Image-specific download history and statistics
- Image file integrity verification
- Automatic cleanup of non-image files
- Cross-platform compatibility

Author: Tool Image Downloader Team
Version: 1.0.0
"""

import sys
import traceback
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from gui.main_window import MainWindow
    from utils.config import config
    from utils.logger import logger
except ImportError as e:
    print(f"Import error: {e}")
    print("Please make sure all dependencies are installed:")
    print("pip install -r requirements.txt")
    sys.exit(1)


def check_dependencies():
    """Check if all required dependencies are available."""
    required_modules = [
        'requests',
        'validators',
        'tqdm',
        'cryptography',
        'schedule'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("Missing required dependencies:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nPlease install them using:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def setup_directories():
    """Create necessary directories."""
    directories = [
        config.get("download_directory", "./images"),
        "logs",
        "data",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


def main():
    """Main application entry point."""
    print("Tool Image Downloader v1.0.0")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Initialize logger
    logger.info("Starting Tool Image Downloader")
    
    try:
        # Create and run the main application
        app = MainWindow()
        logger.info("GUI initialized successfully")
        
        print("Starting Tool Image Downloader GUI...")
        app.run()
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\nApplication interrupted by user")
        
    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        logger.critical(error_msg)
        logger.critical(traceback.format_exc())
        
        print(f"\nError: {error_msg}")
        print("Check the log files for more details.")
        
        # Show error dialog if GUI is available
        try:
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()  # Hide the root window
            messagebox.showerror("Error", f"An unexpected error occurred:\n\n{error_msg}")
            root.destroy()
        except:
            pass
        
        sys.exit(1)
    
    finally:
        logger.info("Application shutdown")


if __name__ == "__main__":
    main()
