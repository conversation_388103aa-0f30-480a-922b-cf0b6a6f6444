# File Downloader Pro

A comprehensive, modern file downloader application built with Python that supports multiple file formats, batch downloads, progress tracking, and advanced features like pause/resume functionality.

## 🚀 Features

### Core Functionality
- **Multi-format Support**: Download images, videos, documents, archives, executables, and more
- **Progress Tracking**: Real-time download progress with speed and ETA indicators
- **Pause/Resume**: Pause and resume downloads at any time
- **Batch Downloads**: Download multiple files simultaneously
- **Queue Management**: Intelligent download queue with configurable concurrent downloads

### Advanced Features
- **Download Scheduling**: Schedule downloads for later execution
- **File Integrity Verification**: Automatic checksum verification when available
- **Authentication Support**: Basic auth, cookies, and custom headers
- **Bandwidth Management**: Speed limiting and bandwidth control
- **Auto-retry**: Automatic retry mechanism for failed downloads
- **Download History**: Complete download history with statistics

### User Interface
- **Modern GUI**: Clean, professional interface using tkinter with themes
- **Drag & Drop**: Drag URLs or files containing URLs
- **Responsive Design**: Adaptive layout that works on different screen sizes
- **Multiple Tabs**: Organized interface with Downloads, History, and Settings tabs
- **Real-time Updates**: Live progress updates and status indicators

### Technical Features
- **Multi-threading**: Asynchronous downloads using thread pools
- **Cross-platform**: Works on Windows, macOS, and Linux
- **Configuration Management**: JSON-based settings with user preferences
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Error Handling**: Robust error handling with user-friendly messages

## 📋 Requirements

- Python 3.7 or higher
- Internet connection for downloads
- Minimum 100MB free disk space

## 🛠️ Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd Download_from_link
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application:**
   ```bash
   python main.py
   ```

## 📦 Dependencies

- `requests` - HTTP library for downloads
- `ttkthemes` - Modern themes for tkinter
- `pillow` - Image processing support
- `validators` - URL validation
- `tqdm` - Progress bar utilities
- `cryptography` - Security and encryption
- `schedule` - Task scheduling

## 🎯 Usage

### Basic Download
1. Launch the application
2. Enter a URL in the URL field
3. Optionally change the destination directory
4. Click "Download"

### Batch Downloads
1. Go to the "Batch Download" section
2. Enter multiple URLs (one per line)
3. Click "Download All"

### Managing Downloads
- **Pause**: Click the "Pause" button next to any active download
- **Resume**: Click "Resume" on paused downloads
- **Cancel**: Use the cancel option to stop downloads

### Settings Configuration
1. Go to the "Settings" tab
2. Adjust download settings:
   - Maximum concurrent downloads
   - Timeout values
   - UI theme
3. Click "Save Settings"

### Download History
- View all past downloads in the "History" tab
- Export history to JSON or CSV format
- Clear history when needed

## ⚙️ Configuration

The application uses a JSON configuration file located at `config/settings.json`. Key settings include:

```json
{
    "download_directory": "./downloads",
    "max_concurrent_downloads": 3,
    "chunk_size": 8192,
    "timeout": 30,
    "max_retries": 3,
    "verify_ssl": true,
    "theme": "arc"
}
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Run all tests
python -m unittest discover tests

# Run specific test files
python -m unittest tests.test_validators
python -m unittest tests.test_downloader
```

## 📁 Project Structure

```
Download_from_link/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── config/
│   └── settings.json      # Configuration file
├── core/
│   ├── downloader.py      # Core download functionality
│   ├── download_manager.py # Download queue management
│   └── file_handler.py    # File operations
├── gui/
│   └── main_window.py     # Main GUI window
├── utils/
│   ├── config.py          # Configuration management
│   ├── logger.py          # Logging utilities
│   └── validators.py      # URL and file validation
├── tests/
│   ├── test_validators.py # Validation tests
│   └── test_downloader.py # Download functionality tests
├── logs/                  # Log files (created at runtime)
├── data/                  # Application data (created at runtime)
└── downloads/             # Default download directory
```

## 🔧 Advanced Usage

### Custom Headers and Authentication
```python
# Example of adding custom headers (for developers)
headers = {
    'Authorization': 'Bearer your-token',
    'User-Agent': 'Custom-Agent/1.0'
}
```

### Scheduling Downloads
The application supports scheduling downloads for later execution. This feature is accessible through the GUI or can be programmed for automated downloads.

### File Organization
Downloaded files can be automatically organized by:
- File type (images, videos, documents, etc.)
- Download date
- Custom categories

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version compatibility

2. **Download Failures**
   - Verify internet connection
   - Check if the URL is accessible
   - Review firewall settings

3. **Permission Errors**
   - Ensure write permissions to download directory
   - Run with appropriate user privileges

4. **GUI Issues**
   - Update tkinter: `pip install --upgrade tkinter`
   - Try different themes in settings

### Log Files
Check log files in the `logs/` directory for detailed error information.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with Python and tkinter
- Uses various open-source libraries
- Inspired by modern download managers

## 📞 Support

For support, please:
1. Check the troubleshooting section
2. Review log files for errors
3. Create an issue on the repository
4. Provide detailed error information

---

**File Downloader Pro** - Making file downloads simple, fast, and reliable.
