"""Unit tests for download functionality."""

import unittest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from core.downloader import FileDownloader, DownloadItem, DownloadStatus
from core.download_manager import DownloadManager


class TestDownloadItem(unittest.TestCase):
    """Test DownloadItem functionality."""
    
    def test_download_item_creation(self):
        """Test download item creation."""
        url = "https://example.com/file.pdf"
        item = DownloadItem(url)
        
        self.assertEqual(item.url, url)
        self.assertEqual(item.status, DownloadStatus.PENDING)
        self.assertEqual(item.progress, 0.0)
        self.assertEqual(item.downloaded_bytes, 0)
        self.assertEqual(item.total_bytes, 0)
        self.assertFalse(item.paused)
        self.assertFalse(item.cancelled)
    
    def test_download_item_with_custom_filename(self):
        """Test download item with custom filename."""
        url = "https://example.com/file.pdf"
        custom_filename = "my_document.pdf"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            item = DownloadItem(url, filename=custom_filename, destination=temp_dir)
            
            self.assertEqual(item.original_filename, custom_filename)
            self.assertEqual(item.filename, custom_filename)
            self.assertEqual(item.destination, Path(temp_dir))
    
    def test_download_item_info(self):
        """Test download item info retrieval."""
        url = "https://example.com/file.pdf"
        item = DownloadItem(url)
        
        info = item.get_info()
        
        self.assertIsInstance(info, dict)
        self.assertIn('url', info)
        self.assertIn('filename', info)
        self.assertIn('status', info)
        self.assertIn('progress', info)
        self.assertEqual(info['url'], url)


class TestFileDownloader(unittest.TestCase):
    """Test FileDownloader functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.downloader = FileDownloader()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_add_download(self):
        """Test adding a download."""
        url = "https://httpbin.org/json"
        
        download_id = self.downloader.add_download(url, destination=self.temp_dir)
        
        self.assertIsNotNone(download_id)
        self.assertIn(download_id, self.downloader.active_downloads)
        
        download_item = self.downloader.active_downloads[download_id]
        self.assertEqual(download_item.url, url)
        self.assertEqual(download_item.status, DownloadStatus.PENDING)
    
    def test_add_invalid_download(self):
        """Test adding an invalid download."""
        invalid_url = "not_a_valid_url"
        
        with self.assertRaises(ValueError):
            self.downloader.add_download(invalid_url)
    
    def test_download_info_retrieval(self):
        """Test download info retrieval."""
        url = "https://httpbin.org/json"
        download_id = self.downloader.add_download(url, destination=self.temp_dir)
        
        info = self.downloader.get_download_info(download_id)
        
        self.assertIsNotNone(info)
        self.assertIsInstance(info, dict)
        self.assertEqual(info['url'], url)
    
    def test_download_info_invalid_id(self):
        """Test download info retrieval with invalid ID."""
        info = self.downloader.get_download_info("invalid_id")
        self.assertIsNone(info)
    
    def test_pause_download(self):
        """Test pausing a download."""
        url = "https://httpbin.org/json"
        download_id = self.downloader.add_download(url, destination=self.temp_dir)
        
        success = self.downloader.pause_download(download_id)
        
        self.assertTrue(success)
        download_item = self.downloader.active_downloads[download_id]
        self.assertTrue(download_item.paused)
        self.assertEqual(download_item.status, DownloadStatus.PAUSED)
    
    def test_cancel_download(self):
        """Test cancelling a download."""
        url = "https://httpbin.org/json"
        download_id = self.downloader.add_download(url, destination=self.temp_dir)
        
        success = self.downloader.cancel_download(download_id)
        
        self.assertTrue(success)
        download_item = self.downloader.active_downloads[download_id]
        self.assertTrue(download_item.cancelled)
        self.assertEqual(download_item.status, DownloadStatus.CANCELLED)
    
    @patch('requests.Session.get')
    def test_download_worker_success(self, mock_get):
        """Test successful download worker execution."""
        # Mock response
        mock_response = Mock()
        mock_response.headers = {'content-length': '100'}
        mock_response.iter_content.return_value = [b'test data'] * 10
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        url = "https://example.com/test.txt"
        download_id = self.downloader.add_download(url, destination=self.temp_dir)
        
        # Run download worker
        self.downloader._download_worker(download_id)
        
        download_item = self.downloader.active_downloads[download_id]
        self.assertEqual(download_item.status, DownloadStatus.COMPLETED)
        self.assertEqual(download_item.progress, 100.0)
    
    @patch('requests.Session.get')
    def test_download_worker_failure(self, mock_get):
        """Test download worker failure handling."""
        # Mock response that raises an exception
        mock_get.side_effect = Exception("Network error")
        
        url = "https://example.com/test.txt"
        download_id = self.downloader.add_download(url, destination=self.temp_dir)
        
        # Run download worker
        self.downloader._download_worker(download_id)
        
        download_item = self.downloader.active_downloads[download_id]
        self.assertEqual(download_item.status, DownloadStatus.FAILED)
        self.assertIn("Network error", download_item.error_message)


class TestDownloadManager(unittest.TestCase):
    """Test DownloadManager functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.manager = DownloadManager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        self.manager.shutdown()
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_add_single_download(self):
        """Test adding a single download."""
        url = "https://httpbin.org/json"
        
        download_id = self.manager.add_download(
            url, destination=self.temp_dir, start_immediately=False
        )
        
        self.assertIsNotNone(download_id)
        self.assertIn(download_id, self.manager.downloader.active_downloads)
    
    def test_add_batch_downloads(self):
        """Test adding batch downloads."""
        urls = [
            "https://httpbin.org/json",
            "https://httpbin.org/xml",
            "https://httpbin.org/html"
        ]
        
        download_ids = self.manager.add_batch_downloads(
            urls, destination=self.temp_dir
        )
        
        self.assertEqual(len(download_ids), len(urls))
        for download_id in download_ids:
            self.assertIn(download_id, self.manager.downloader.active_downloads)
    
    def test_queue_status(self):
        """Test queue status retrieval."""
        status = self.manager.get_queue_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn('queued_downloads', status)
        self.assertIn('active_downloads', status)
        self.assertIn('scheduled_downloads', status)
    
    def test_get_all_downloads(self):
        """Test getting all downloads."""
        url = "https://httpbin.org/json"
        self.manager.add_download(url, destination=self.temp_dir, start_immediately=False)
        
        all_downloads = self.manager.get_all_downloads()
        
        self.assertIsInstance(all_downloads, list)
        self.assertGreater(len(all_downloads), 0)
        
        for download in all_downloads:
            self.assertIsInstance(download, dict)
            self.assertIn('download_id', download)
            self.assertIn('url', download)
    
    def test_pause_resume_download(self):
        """Test pausing and resuming downloads."""
        url = "https://httpbin.org/json"
        download_id = self.manager.add_download(
            url, destination=self.temp_dir, start_immediately=False
        )
        
        # Test pause
        success = self.manager.pause_download(download_id)
        self.assertTrue(success)
        
        # Test resume
        success = self.manager.resume_download(download_id)
        self.assertTrue(success)
    
    def test_cancel_download(self):
        """Test cancelling a download."""
        url = "https://httpbin.org/json"
        download_id = self.manager.add_download(
            url, destination=self.temp_dir, start_immediately=False
        )
        
        success = self.manager.cancel_download(download_id)
        self.assertTrue(success)
    
    def test_cancel_all_downloads(self):
        """Test cancelling all downloads."""
        urls = [
            "https://httpbin.org/json",
            "https://httpbin.org/xml"
        ]
        
        for url in urls:
            self.manager.add_download(url, destination=self.temp_dir, start_immediately=False)
        
        self.manager.cancel_all_downloads()
        
        # Check that queue is empty
        status = self.manager.get_queue_status()
        self.assertEqual(status['queued_downloads'], 0)


if __name__ == '__main__':
    unittest.main()
