"""Core download functionality with threading and progress tracking."""

import os
import time
import threading
from pathlib import Path
from typing import Optional, Callable, Dict, Any
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from utils.config import config
from utils.logger import logger
from utils.validators import URLValidator, FileValidator


class DownloadStatus:
    """Download status enumeration."""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DownloadItem:
    """Represents a single download item."""
    
    def __init__(self, url: str, filename: Optional[str] = None, 
                 destination: Optional[str] = None):
        self.url = url
        self.original_filename = filename
        self.destination = Path(destination) if destination else Path(config.get("download_directory"))
        self.status = DownloadStatus.PENDING
        self.progress = 0.0
        self.downloaded_bytes = 0
        self.total_bytes = 0
        self.speed = 0.0
        self.eta = 0
        self.error_message = ""
        self.start_time = None
        self.end_time = None
        self.paused = False
        self.cancelled = False
        
        # Auto-detect filename if not provided
        if not self.original_filename:
            self.original_filename = URLValidator.extract_filename_from_url(url)
        
        if not self.original_filename:
            # Generate filename from URL
            parsed = urlparse(url)
            self.original_filename = Path(parsed.path).name or "download"
        
        # Sanitize filename
        self.filename = URLValidator.sanitize_filename(self.original_filename)
        
        # Ensure unique filename
        self.filename = URLValidator.generate_unique_filename(self.destination, self.filename)
        
        # Full file path
        self.file_path = self.destination / self.filename
    
    def get_info(self) -> Dict[str, Any]:
        """Get download item information."""
        return {
            'url': self.url,
            'filename': self.filename,
            'destination': str(self.destination),
            'status': self.status,
            'progress': self.progress,
            'downloaded_bytes': self.downloaded_bytes,
            'total_bytes': self.total_bytes,
            'speed': self.speed,
            'eta': self.eta,
            'error_message': self.error_message
        }


class FileDownloader:
    """Main file downloader class with threading support."""
    
    def __init__(self):
        self.session = self._create_session()
        self.active_downloads = {}
        self.download_threads = {}
        self.progress_callbacks = {}
        
    def _create_session(self) -> requests.Session:
        """Create a requests session with retry strategy."""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=config.get("max_retries", 3),
            backoff_factor=config.get("retry_delay", 2),
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set headers
        session.headers.update({
            'User-Agent': config.get("user_agent", "FileDownloader/1.0")
        })
        
        return session
    
    def add_download(self, url: str, filename: Optional[str] = None,
                    destination: Optional[str] = None,
                    progress_callback: Optional[Callable] = None) -> str:
        """Add a new download to the queue."""
        if not URLValidator.is_valid_url(url):
            raise ValueError(f"Invalid URL: {url}")
        
        download_item = DownloadItem(url, filename, destination)
        download_id = str(id(download_item))
        
        self.active_downloads[download_id] = download_item
        if progress_callback:
            self.progress_callbacks[download_id] = progress_callback
        
        logger.info(f"Added download: {download_item.filename} from {url}")
        return download_id
    
    def start_download(self, download_id: str) -> bool:
        """Start downloading a file."""
        if download_id not in self.active_downloads:
            return False
        
        download_item = self.active_downloads[download_id]
        
        if download_item.status == DownloadStatus.DOWNLOADING:
            return True
        
        # Create download thread
        thread = threading.Thread(
            target=self._download_worker,
            args=(download_id,),
            daemon=True
        )
        
        self.download_threads[download_id] = thread
        thread.start()
        
        return True
    
    def pause_download(self, download_id: str) -> bool:
        """Pause a download."""
        if download_id not in self.active_downloads:
            return False
        
        download_item = self.active_downloads[download_id]
        download_item.paused = True
        download_item.status = DownloadStatus.PAUSED
        
        logger.download_paused(download_item.filename)
        return True
    
    def resume_download(self, download_id: str) -> bool:
        """Resume a paused download."""
        if download_id not in self.active_downloads:
            return False
        
        download_item = self.active_downloads[download_id]
        if download_item.status != DownloadStatus.PAUSED:
            return False
        
        download_item.paused = False
        download_item.status = DownloadStatus.DOWNLOADING
        
        logger.download_resumed(download_item.filename)
        return self.start_download(download_id)
    
    def cancel_download(self, download_id: str) -> bool:
        """Cancel a download."""
        if download_id not in self.active_downloads:
            return False
        
        download_item = self.active_downloads[download_id]
        download_item.cancelled = True
        download_item.status = DownloadStatus.CANCELLED
        
        # Clean up partial file
        if download_item.file_path.exists():
            try:
                download_item.file_path.unlink()
            except Exception as e:
                logger.error(f"Failed to delete partial file: {e}")
        
        return True
    
    def get_download_info(self, download_id: str) -> Optional[Dict[str, Any]]:
        """Get download information."""
        if download_id not in self.active_downloads:
            return None
        
        return self.active_downloads[download_id].get_info()
    
    def _download_worker(self, download_id: str) -> None:
        """Worker thread for downloading files."""
        download_item = self.active_downloads[download_id]
        
        try:
            download_item.status = DownloadStatus.DOWNLOADING
            download_item.start_time = time.time()
            
            logger.download_started(download_item.url, download_item.filename)
            
            # Create destination directory
            download_item.destination.mkdir(parents=True, exist_ok=True)
            
            # Check if partial download exists
            resume_header = {}
            if download_item.file_path.exists():
                download_item.downloaded_bytes = download_item.file_path.stat().st_size
                resume_header['Range'] = f'bytes={download_item.downloaded_bytes}-'
            
            # Make request
            response = self.session.get(
                download_item.url,
                headers=resume_header,
                stream=True,
                timeout=config.get("timeout", 30),
                verify=config.get("verify_ssl", True)
            )
            response.raise_for_status()
            
            # Get total file size
            if 'content-length' in response.headers:
                content_length = int(response.headers['content-length'])
                if resume_header:
                    download_item.total_bytes = download_item.downloaded_bytes + content_length
                else:
                    download_item.total_bytes = content_length
            
            # Download file
            mode = 'ab' if resume_header else 'wb'
            chunk_size = config.get("chunk_size", 8192)
            
            with open(download_item.file_path, mode) as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if download_item.cancelled:
                        return
                    
                    if download_item.paused:
                        while download_item.paused and not download_item.cancelled:
                            time.sleep(0.1)
                        if download_item.cancelled:
                            return
                    
                    if chunk:
                        f.write(chunk)
                        download_item.downloaded_bytes += len(chunk)
                        
                        # Update progress
                        if download_item.total_bytes > 0:
                            download_item.progress = (download_item.downloaded_bytes / download_item.total_bytes) * 100
                        
                        # Calculate speed and ETA
                        elapsed_time = time.time() - download_item.start_time
                        if elapsed_time > 0:
                            download_item.speed = download_item.downloaded_bytes / elapsed_time
                            if download_item.speed > 0 and download_item.total_bytes > 0:
                                remaining_bytes = download_item.total_bytes - download_item.downloaded_bytes
                                download_item.eta = remaining_bytes / download_item.speed
                        
                        # Call progress callback
                        if download_id in self.progress_callbacks:
                            self.progress_callbacks[download_id](download_item.get_info())
            
            download_item.status = DownloadStatus.COMPLETED
            download_item.end_time = time.time()
            download_item.progress = 100.0
            
            duration = download_item.end_time - download_item.start_time
            logger.download_completed(download_item.filename, download_item.downloaded_bytes, duration)
            
        except Exception as e:
            download_item.status = DownloadStatus.FAILED
            download_item.error_message = str(e)
            logger.download_failed(download_item.url, download_item.filename, str(e))
        
        finally:
            # Final progress callback
            if download_id in self.progress_callbacks:
                self.progress_callbacks[download_id](download_item.get_info())
